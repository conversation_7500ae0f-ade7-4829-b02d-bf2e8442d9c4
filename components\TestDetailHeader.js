import {FlatList, Platform, Text, useWindowDimensions, View} from 'react-native';
import CountDown from 'react-native-countdown-fixed';
import {Colors} from '../constants/styles';
import QuestionGridTile from './QuestionGridTile';
import * as Progress from 'react-native-progress';
import {useState} from 'react';
import TextNote from "../ui/TextNote";

function TestDetailHeader({totalTime, questionNumberData, questionNumberPress, isFinish, onTimeUp, updateRemainTime, remainTime}) {
  const [remainTimeState, setRemainTimeState] = useState(remainTime);
  const {width} = useWindowDimensions();
  let questionNumColumn = Math.floor((width - 42) / 34);

  function renderButtonList(itemData) {
    return <QuestionGridTile title={itemData.item.index + 1}
                             reviewed={itemData.item.reviewed}
                             answered={itemData.item.answered}
                             isCurrent={itemData.item.current}
                             isFinish={isFinish}
                             correct={itemData.item.correct}
                             onPress={() => questionNumberPress(itemData.item.index)} />;
  }

  function onChangeCountDown(value) {
    setRemainTimeState(value);
    if (updateRemainTime) {
      updateRemainTime(value);
    }
  }

  return (
    <View style={{justifyContent: 'center', alignItems: 'center'}}>

      {!isFinish && <View style={{marginVertical: 5}}>
        <Progress.Bar color={Colors.primary100} progress={remainTimeState / totalTime} width={width - 30} />

        <View style={{alignItems: 'flex-end'}}>
          <CountDown
            size={15}
            until={remainTime}
            onChange={(value) => {
              onChangeCountDown(value);
            }}
            onFinish={onTimeUp}
            digitStyle={{
              backgroundColor: Colors.background,
              width: 20,
              height: 20,
              borderWidth: 0,
              borderColor: Colors.primary900
            }}
            digitTxtStyle={{color: Colors.primary100}}
            timeLabelStyle={{color: Colors.primary100}}
            separatorStyle={{color: Colors.primary100}}
            timeToShow={['H', 'M', 'S']}
            timeLabels={{m: null, s: null}}
            showSeparator
          />
        </View>
      </View>}

      <View style={{marginTop: 0, borderColor: Colors.primary100, borderWidth: 1, padding: 5}}>
        <FlatList style={{backgroundColor: Colors.accent250, flexGrow: 0, maxHeight: 74, width: width - 42}}
                  data={questionNumberData}
                  keyExtractor={(item) => item.index}
                  renderItem={renderButtonList}
                  key={questionNumColumn}
                  numColumns={questionNumColumn} />

        {!isFinish && (width > 400) &&
         <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 5}}>
           <TextNote color='white'
                     style={{borderColor: Colors.primary800, borderWidth: 1, marginLeft: 0}}>Current</TextNote>
           <TextNote color={Colors.primary600}>Review</TextNote>
           <TextNote color={Colors.accent200}>Answered</TextNote>
           <TextNote color='white'>Not Answered</TextNote>
         </View>}

        {!isFinish && (width <= 400) &&
         <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 5}}>
           <TextNote color='white'
                     style={{borderColor: Colors.primary800, borderWidth: 1, marginLeft: 0}}>Current</TextNote>
           <TextNote color={Colors.primary600} style={{marginLeft: 15}}>Review</TextNote>
           <TextNote color={Colors.accent200} style={{marginLeft: 15}}>Answered</TextNote>
         </View>}

        {isFinish && <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 5}}>
          <TextNote color={Colors.primary900} style={{marginRight: 8, marginLeft: 0}}>Correct</TextNote>
          <TextNote color={Colors.error500} style={{marginRight: 8, marginLeft: 15}}>Incorrect</TextNote>
        </View>}
      </View>
    </View>
  );
}

export default TestDetailHeader;
