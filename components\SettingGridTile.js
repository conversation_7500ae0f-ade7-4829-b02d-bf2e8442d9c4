import GridTile from './GridTile';
import {EvilIcons, MaterialCommunityIcons} from '@expo/vector-icons';
import {StyleSheet, Text, View} from 'react-native';
import {Colors} from '../constants/styles';

function SettingGridTile({onPress, icon, text, isRightIcon, style}) {
  return (
    <GridTile onPress={onPress} style={[styles.gridTile, style]}>
      <View style={styles.itemContainer}>
        <MaterialCommunityIcons name={icon} size={24} color={Colors.primary100} />
        <Text style={styles.title}>{text}</Text>
        {isRightIcon && <EvilIcons name="chevron-right" size={24} color="black" />}
      </View>
    </GridTile>
  );
}

export default SettingGridTile;

const styles = StyleSheet.create({
  gridTile: {
    height: 57,
    marginHorizontal: 10,
    marginTop: 22
  },
  itemContainer: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10
  },
  title: {
    flex: 1,
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.accent100,
    marginLeft: 5
  }
});
