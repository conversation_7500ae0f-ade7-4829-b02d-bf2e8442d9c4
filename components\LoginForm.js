import IconInput from '../ui/IconInput';
import {
    Alert,
    Linking,
    Pressable, ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity, View
} from 'react-native';
import Button from '../ui/Button';
import Checkbox from 'expo-checkbox';
import React, {useState} from 'react';
import {Colors} from '../constants/styles';
import {useNavigation} from '@react-navigation/native';
import validator from 'validator';
import {TERMS_AND_CONDITIONS} from "../constants/constants";
import FastImage from "react-native-fast-image";
import ConfirmationInput from "../ui/ConfirmationInput";
import {generateVerification} from "../util/AuthApi";
import VerificationType from "../constants/VerificationType";

function LoginForm({isLogin, onSubmit}) {
    const navigation = useNavigation();

    const [checkboxValue, setCheckboxValue] = useState(false);
    const [enteredEmail, setEnteredEmail] = useState('');
    const [confirmationCode, setConfirmationCode] = useState(null);
    const [enteredPassword, setEnteredPassword] = useState('');
    const [enteredConfirmPassword, setEnteredConfirmPassword] = useState('');

    function checkboxOnValueChange(newValue) {
        setCheckboxValue(newValue);
    }

    function updateInputValueHandler(inputType, enteredValue) {
        switch (inputType) {
            case 'email':
                setEnteredEmail(enteredValue);
                break;
            case 'confirmationCode':
                setConfirmationCode(enteredValue);
                break;
            case 'password':
                setEnteredPassword(enteredValue);
                break;
            case 'confirmPassword':
                setEnteredConfirmPassword(enteredValue);
                break;
        }
    }

    function submitHandler() {
        const email = enteredEmail.trim();
        const password = enteredPassword.trim();
        const confirmPassword = enteredConfirmPassword ? enteredConfirmPassword.trim() : null;
        const confirmCode = confirmationCode ? confirmationCode.trim() : null;

        const emailIsValid = validator.isEmail(email);
        const passwordIsValid = password.length >= 8;
        const passwordsAreEqual = password === confirmPassword;

        if (!isLogin && !confirmCode) {
            Alert.alert('Alert', 'Please enter confirmation code.');
            return;
        }

        if (!email || !password) {
            Alert.alert('Alert', 'Please enter both email and password.');
            return;
        }

        if (!emailIsValid) {
            Alert.alert('Alert', 'Please enter a valid email address.');
            return;
        }

        if (!passwordIsValid) {
            Alert.alert('Alert', 'Please enter a password of at least 8 characters.');
            return;
        }

        if (!isLogin && !passwordsAreEqual) {
            Alert.alert('Alert', 'The password and confirmation password do not match. Please try again.');
            return;
        }

        if (!isLogin && !checkboxValue) {
            Alert.alert('Alert', 'Please agree with Terms and Conditions.');
            return;
        }

        onSubmit({email, password, confirmationCode});
    }

    function checkEmail() {
        return enteredEmail && validator.isEmail(enteredEmail);
    }

    async function sendConfirmationCode() {
        try {
            const emailIsValid = checkEmail();

            if (!emailIsValid) {
                Alert.alert('Alert', 'Please enter a valid email address.');
            } else {
                await generateVerification(enteredEmail.trim(), VerificationType.REGISTER, false);
                return true;
            }
        } catch (e) {
            console.log(e);
            Alert.alert(
                "Fail",
                "An error occurred, please try again later",
                [{
                    text: "OK"
                }]
            );
        }
        return false;
    }

    return (
        <View style={{flex: 1}}>
            <ScrollView keyboardShouldPersistTaps='handled' contentContainerStyle={styles.container}>
                <View style={styles.imageContainer}>
                    <FastImage style={{width: 90, height: 90}} source={require('../assets/icon-user.png')}/>
                </View>

                <View style={styles.inputContainer}>
                    <IconInput placeholder='Email' leftIconName='user' rightIconName='x-circle'
                               onUpdateValue={updateInputValueHandler.bind(this, 'email')}/>

                    {!isLogin &&
                        <ConfirmationInput
                            placeholder='Email confirmation code'
                            onSendCode={sendConfirmationCode}
                            disabled={!checkEmail()}
                            onUpdateValue={updateInputValueHandler.bind(this, 'confirmationCode')}/>}

                    <IconInput placeholder='Password (at least 8 characters)' isSecure={true} leftIconName='lock'
                               rightIconName='eye'
                               onUpdateValue={updateInputValueHandler.bind(this, 'password')}/>

                    {!isLogin &&
                        <IconInput placeholder='Confirm password' isSecure={true} leftIconName='lock'
                                   rightIconName='eye'
                                   onUpdateValue={updateInputValueHandler.bind(this, 'confirmPassword')}/>}
                </View>

                {isLogin && <View style={styles.forgetContainer}>
                    <TouchableOpacity onPress={() => navigation.navigate("ForgetPassword")}>
                        <Text style={styles.forgetText}>Forgot password?</Text>
                    </TouchableOpacity>
                </View>}

                {!isLogin && <View style={styles.termContainer}>
                    <Checkbox style={styles.termCheckbox} disabled={false}
                              value={checkboxValue}
                              onValueChange={checkboxOnValueChange}/>
                    <Text style={styles.termText}>I agree with </Text>

                    <TouchableOpacity onPress={() => Linking.openURL(TERMS_AND_CONDITIONS)}>
                        <Text style={[styles.termText, styles.termTextLink]}>
                            Terms and Conditions.
                        </Text>
                    </TouchableOpacity>
                </View>}

                <View style={styles.buttonContainer}>
                    <Button style={styles.button} onPress={submitHandler}>{isLogin ? 'Login' : 'Continue'}</Button>
                    {/*{isLogin && <Pressable>*/}
                    {/*  <Image source={require('../assets/face-id.png')} />*/}
                    {/*</Pressable>}*/}
                </View>

                {isLogin && <View style={styles.registerContainer}>
                    <Text style={styles.registerText}>Don’t have an account? </Text>
                    <Pressable onPress={() => {
                        navigation.navigate('Register');
                    }}>
                        <Text style={[styles.registerText, styles.registerTextLink]}>Register</Text>
                    </Pressable>
                </View>}

                {!isLogin && <View style={styles.registerContainer}>
                    <Text style={styles.registerText}>Have an account? </Text>
                    <Pressable onPress={() => {
                        navigation.navigate('Login');
                    }}>
                        <Text style={[styles.registerText, styles.registerTextLink]}>Login</Text>
                    </Pressable>
                </View>}
            </ScrollView>
        </View>
    );
}

export default LoginForm;

const styles = StyleSheet.create({
    container: {
        marginHorizontal: 10,
        flex: 1,
        marginTop: 50
    },
    imageContainer: {
        alignItems: 'center'
    },
    termContainer: {
        flexDirection: 'row',
        marginTop: 15,
        justifyContent: 'center',
        alignItems: "center",
        flexWrap: "wrap"
    },
    termCheckbox: {
        marginRight: 15,
        transform: [{scaleX: 0.8}, {scaleY: 0.8}]
    },
    termText: {
        fontSize: 17,
        fontStyle: 'italic',
        fontWeight: 'bold',
        color: Colors.accent100
    },
    termTextLink: {
        color: Colors.primary100,
        textDecorationLine: 'underline'
    },
    forgetContainer: {
        marginTop: 20,
        marginRight: 30,
        alignItems: 'flex-end'
    },
    forgetText: {
        fontSize: 16,
        fontWeight: '600',
        textDecorationLine: 'underline',
        color: Colors.accent100
    },
    registerContainer: {
        flexDirection: 'row',
        marginTop: 15,
        justifyContent: 'center',
        alignItems: "center",
        flexWrap: 'wrap'
    },
    registerText: {
        fontSize: 16,
        fontWeight: '600',
        color: Colors.accent100
    },
    registerTextLink: {
        textDecorationLine: 'underline',
        color: Colors.primary100,
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: 30
    },
    button: {
        width: 237,
        height: 50,
        marginRight: 20
    }
});
