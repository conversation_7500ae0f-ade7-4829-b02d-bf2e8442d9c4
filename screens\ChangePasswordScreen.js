import {<PERSON><PERSON>, <PERSON><PERSON>View, StyleSheet, Text, View} from 'react-native';
import Input from '../ui/Input';
import {Colors} from '../constants/styles';
import Button from '../ui/Button';
import React, {useContext, useState} from 'react';
import {updatePass} from '../util/UserApi';
import {AuthContext} from '../store/AuthContext';
import LoadingOverlay from '../ui/LoadingOverlay';
import {BANNER_AD_UNIT_ID, SHOW_BANNER_AD} from '../constants/constants';
import {BannerAd, BannerAdSize} from 'react-native-google-mobile-ads';

function ChangePasswordScreen() {
    const [isAuthenticating, setIsAuthenticating] = useState(false);
    const [oldPassword, setOldPassword] = useState(null);
    const [newPassword, setNewPassword] = useState(null);
    const [confirmNewPassword, setConfirmNewPassword] = useState(null);
    const [newPassMatch, setNewPassMatch] = useState(true);
    const [duplicatedPass, setDuplicatedPass] = useState(false);
    const [enableButton, setEnableButton] = useState(false);

    const authCtx = useContext(AuthContext);

    async function savePressHandler() {
        try {
            setIsAuthenticating(true);
            const response = await updatePass(authCtx.token, oldPassword, newPassword);
            console.log(response);
            if (response.code === 200) {
                Alert.alert(
                    "Success",
                    "Account password has been changed successfully",
                    [{
                        text: "OK",
                        onPress: () => authCtx.logout()
                    }]
                );
            } else {
                Alert.alert(
                    "Fail",
                    response.message,
                    [{
                        text: "OK"
                    }]
                );
            }
        } catch (e) {
            console.log(e);
            Alert.alert(
                "Fail",
                "An error occurred, please try again later",
                [{
                    text: "OK"
                }]
            );
        } finally {
            setIsAuthenticating(false);
        }
    }

    function updateData(value, type) {
        switch (type) {
            case "oldPass":
                setOldPassword(value);
                checkData(value, newPassword, confirmNewPassword);
                break;
            case "newPass":
                setNewPassword(value);
                checkData(oldPassword, value, confirmNewPassword);
                break;
            case "confirmPass":
                setConfirmNewPassword(value);
                checkData(oldPassword, newPassword, value);
                break;
        }
    }

    function checkData(oldPass, newPass, confirmPass) {
        const allFieldNotBlank = oldPass && newPass && confirmPass;

        const passMatch = (newPass === confirmPass) || !newPass || !confirmPass;
        setNewPassMatch(passMatch);

        const duplicatedPass = (newPass === oldPass) && !!newPass && !!oldPass;
        setDuplicatedPass(duplicatedPass)

        setEnableButton(allFieldNotBlank && passMatch && !duplicatedPass);
    }

    if (isAuthenticating) {
        return <LoadingOverlay message="Updating..."/>;
    }

    return (
        <>
            <View style={{flex: 1}}>
                <ScrollView keyboardShouldPersistTaps='handled' contentContainerStyle={styles.container}>
                    <Input label='Old password' placeholder='Enter old password' secure={true} rightIcon='eye'
                           onUpdateValue={(enteredValue) => updateData(enteredValue, "oldPass")}
                           rightIconColor={Colors.accent400}/>

                    <Input label='New password' placeholder='Enter new password' secure={true} rightIcon='eye'
                           onUpdateValue={(enteredValue) => updateData(enteredValue, "newPass")}
                           rightIconColor={Colors.accent400}/>
                    {duplicatedPass &&
                        <Text style={styles.errorText}>The new password cannot be the same as the old password.</Text>}

                    <Input label='Confirm new password' placeholder='Enter confirm new password' secure={true}
                           rightIcon='eye'
                           onUpdateValue={(enteredValue) => updateData(enteredValue, "confirmPass")}
                           rightIconColor={Colors.accent400}/>
                    {!newPassMatch && <Text style={styles.errorText}>Passwords do not match.</Text>}

                    <View style={styles.buttonContainer}>
                        <Button disable={!enableButton} style={styles.button} onPress={savePressHandler}>Save</Button>
                    </View>
                </ScrollView>
            </View>
            {SHOW_BANNER_AD && <BannerAd
                unitId={BANNER_AD_UNIT_ID}
                size={BannerAdSize.ANCHORED_ADAPTIVE_BANNER}
                requestOptions={{
                    requestNonPersonalizedAdsOnly: true,
                }}
            />}
        </>
    );
}

export default ChangePasswordScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        marginHorizontal: 10
    },
    buttonContainer: {
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: 40
    },
    button: {
        width: 237,
        height: 50
    },
    errorText: {
        color: Colors.error500,
        marginTop: 10
    }
});
