import {Pressable, StyleSheet, Text, View} from 'react-native';
import {Colors} from '../constants/styles';

function Button({children, onPress, disable, style, textStyle}) {
  const backgroundStyle = {
    backgroundColor: disable ? Colors.accent400 : Colors.primary100
  }

  return (
    <Pressable
      disabled={disable}
      style={({pressed}) => [styles.button, backgroundStyle, style, pressed && styles.pressed]}
      onPress={onPress}
    >
      <View>
        <Text style={[styles.buttonText, textStyle]}>{children}</Text>
      </View>
    </Pressable>
  );
}

export default Button;

const styles = StyleSheet.create({
  button: {
    borderRadius: 6,
    paddingVertical: 6,
    paddingHorizontal: 12,
    elevation: 2,
    shadowColor: 'black',
    shadowOffset: {width: 1, height: 1},
    shadowOpacity: 0.25,
    shadowRadius: 4,
    justifyContent: 'center'
  },
  pressed: {
    opacity: 0.7,
  },
  buttonText: {
    textAlign: 'center',
    color: 'white',
    fontSize: 22,
    fontWeight: 'bold'
  }
});
