import {Image, StyleSheet, Text} from 'react-native';
import {Colors} from '../constants/styles';
import GridTile from './GridTile';
import FastImage from "react-native-fast-image";

function CategoryGridTile({imageUri, numFullTest, numMiniTest, onPress, style}) {
  return (
    <GridTile onPress={onPress} style={{height: 160, ...style}}>
      <FastImage resizeMode={FastImage.resizeMode.stretch}
                 style={{height: 130, width: 130}}
                 source={{uri: imageUri}} />
      {/*<Text style={[styles.text, {marginTop: 5}]}>{numFullTest} Full tests</Text>*/}
      {/*<Text style={styles.text}>{numMiniTest} Mini tests</Text>*/}
    </GridTile>
  );
}

export default CategoryGridTile;

const styles = StyleSheet.create({
  text: {
    fontWeight: 'bold',
    fontSize: 15,
    color: Colors.accent100
  }
});
