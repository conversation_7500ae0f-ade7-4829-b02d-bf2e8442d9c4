import {Pressable, StyleSheet, Text, View} from 'react-native';
import {Colors} from '../constants/styles';

function IconButton({children, onPress, disable, style, textStyle, leftIcon, rightIcon}) {
  const textAlignStyle = {textAlign: rightIcon ? 'right' : 'left'};
  return (
    <Pressable
      disabled={disable}
      style={({pressed}) => [styles.button, style, pressed && styles.pressed, disable && styles.disabled]}
      onPress={onPress}
    >
      <View style={styles.buttonContainer}>
        {leftIcon && <View style={styles.leftIcon}>{leftIcon}</View>}
        <Text style={[styles.buttonText, textStyle, textAlignStyle]}>{children}</Text>
        {rightIcon && <View style={styles.leftIcon}>{rightIcon}</View>}
      </View>
    </Pressable>
  );
}

export default IconButton;

const styles = StyleSheet.create({
  button: {
    borderRadius: 6,
    backgroundColor: Colors.primary100,
    elevation: 2,
    shadowColor: 'black',
    shadowOffset: {width: 1, height: 1},
    shadowOpacity: 0.25,
    shadowRadius: 4,
    justifyContent: 'center'
  },
  disabled: {
    backgroundColor: Colors.accent400
  },
  pressed: {
    opacity: 0.7,
  },
  buttonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexGrow: 1
  },
  leftIcon: {
    marginHorizontal: 5
  },
  buttonText: {
    flex: 1,
    color: 'white',
    fontSize: 22,
    fontWeight: 'bold',
    flexGrow: 1,
    marginBottom: 2
  }
});
