import {createContext, useState} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import jwt_decode from "jwt-decode";

export const AuthContext = createContext({
  token: '',
  email: '',
  isAuthenticated: false,
  isLoading: true,
  loadToken: () => {
  },
  authentication: (token, email) => {
  },
  logout: () => {
  }
});

function AuthProvider({children}) {
  const [authToken, setAuthToken] = useState({});
  const [isLoading, setIsLoading] = useState(true);

  async function authentication(token, email) {
    try {
      const auth = {
        'token': token,
        'email': email
      };
      setAuthToken(auth);
      await AsyncStorage.setItem('token', JSON.stringify(auth));
    } catch (ex) {
      console.log(ex)
    }
  }

  async function logout() {
    try {
      setAuthToken(null);
      await AsyncStorage.removeItem('token');
    } catch (ex) {
      console.log(ex)
    }
  }

  async function loadToken() {
    try {
      setIsLoading(true);
      const tokenStr = await AsyncStorage.getItem('token');
      setAuthToken(JSON.parse(tokenStr));
    } catch (ex) {
      console.log(ex)
      await logout();
    } finally {
      setIsLoading(false);
    }
  }

  function isAuthenticated() {
    try {
      if (authToken && authToken.token && authToken.email) {
        let decodedToken = jwt_decode(authToken.token);
        let currentDate = new Date();

        // JWT exp is in seconds
        console.info(decodedToken.exp);
        if (decodedToken.exp * 1000 < currentDate.getTime() - 86400 * 1000) {
          console.warn("Token expired.");
          return false;
        } else {
          console.info("Valid token");
          return true;
        }
      } else {
        return false;
      }
    } catch (ex) {
      console.log(ex);
    }
    return false;
  }

  const value = {
    token: authToken ? authToken.token : '',
    email: authToken ? authToken.email : '',
    loadToken: loadToken,
    isLoading: isLoading,
    isAuthenticated: isAuthenticated(),
    authentication: authentication,
    logout: logout
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export default AuthProvider;
