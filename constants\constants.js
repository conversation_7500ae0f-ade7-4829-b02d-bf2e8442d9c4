import {TestIds} from 'react-native-google-mobile-ads';
import {Platform} from 'react-native';

export const BE_URL = "https://udecareer.com:8081";
export const BANNER_AD_UNIT_ID =
    process.env.ENV_NAME === 'DEV' || __DEV__ ? TestIds.BANNER
        : (Platform.OS === 'ios'
            ? 'ca-app-pub-8250110077262376/1820668349'
            : 'ca-app-pub-8250110077262376/5294408298');

export const BANNER_ARTICLE_AD_UNIT_ID =
    process.env.ENV_NAME === 'DEV' || __DEV__ ? TestIds.BANNER
        : (Platform.OS === 'ios'
            ? 'ca-app-pub-8250110077262376/3223778457'
            : 'ca-app-pub-8250110077262376/4928839841');
export const INTERSTITIAL_AD_UNIT_ID =
    process.env.ENV_NAME === 'DEV' || __DEV__ ? TestIds.INTERSTITIAL
        : (Platform.OS === 'ios'
            ? 'ca-app-pub-8250110077262376/2823787468'
            : 'ca-app-pub-8250110077262376/6981953501');
export const SHOW_BANNER_AD = true;

export const TYPE_TEST_FULL_CODE = "full";
export const TYPE_TEST_MINI_CODE = "mini";

export const TERMS_AND_CONDITIONS = "https://www.udecareer.com/terms-and-conditions/";
export const PRIVACY_POLICY = "https://www.udecareer.com/privacy-policy-2/";

export const ENABLE_REVENUE_CAT = true;

export const REVENUE_CAT_API_KEYS = {
    apple: "appl_wfHSufxsDPnVAXMZnFTULELpBZv",
    google: "goog_rmKHnvfBcIRozqysURYKVKVMMKF",
};
