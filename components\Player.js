import React, {useContext, useRef, useState} from 'react';
import {StyleSheet, Text, View} from 'react-native';
import SeekBar from './SeekBar';
import Controls from './Controls';
import Video from 'react-native-video';
import {QuizContext} from '../store/QuizContext';

export default function Player({audioUrl, audioOnly}) {
    const quizCtx = useContext(QuizContext);

    const [state, setState] = useState({
        paused: true,
        totalLength: 1,
        currentPosition: 0
    });
    const audioRef = useRef();

    async function setDuration(data) {
        const isAutoPlay = await quizCtx.isAutoPlay();
        setState({...state, totalLength: Math.floor(data.duration), paused: !isAutoPlay});
    }

    function setTime(data) {
        setState({...state, currentPosition: Math.floor(data.currentTime)});
    }

    function seek(time) {
        time = Math.round(time);
        audioRef.current && audioRef.current.seek(time);
        setState({...state, currentPosition: time, paused: false});
    }

    function videoRender() {
        if (audioOnly) {
            return state.isChanging ? null : (
                <Video source={{uri: audioUrl}} // Can be a URL or a local file.
                       ref={(ref) => (audioRef.current = ref)}
                       playInBackground={false}
                       playWhenInactive={false}
                       audioOnly={audioOnly}
                       ignoreSilentSwitch={"ignore"}
                       paused={state.paused}               // Pauses playback entirely.
                       resizeMode="cover"           // Fill the whole screen at aspect ratio.
                       repeat={false}                // Repeat forever.
                       onLoad={setDuration.bind(this)}    // Callback when video loads
                       onProgress={setTime.bind(this)}    // Callback every ~250ms with currentTime
                       style={styles.audioElement}/>
            );
        } else {
            return state.isChanging ? null : (
                <Video
                    source={{uri: audioUrl}} // Can be a URL or a local file.
                    ref={(ref) => (audioRef.current = ref)}
                    controls={true}
                    playInBackground={false}
                    playWhenInactive={false}
                    ignoreSilentSwitch={"ignore"}
                    paused={true}               // Pauses playback entirely.
                    resizeMode="cover"           // Fill the whole screen at aspect ratio.
                    repeat={false}                // Repeat forever.
                    style={styles.videoElement}/>
            );
        }
    }

    return (
        <>
            {audioOnly && <View style={styles.container}>
                <Controls
                    onPressPlay={() => setState({...state, paused: false})}
                    onPressPause={() => setState({...state, paused: true})}
                    paused={state.paused}/>
                <View style={styles.seekBar}>
                    <SeekBar
                        onSeek={seek.bind(this)}
                        trackLength={state.totalLength}
                        onSlidingStart={() => setState({...state, paused: true})}
                        currentPosition={state.currentPosition}
                    />
                </View>
                {videoRender()}
            </View>}

            {!audioOnly && <View style={styles.viewStyle}>
                {videoRender()}
            </View>}
        </>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#ffffff',
        flexDirection: "row",
        justifyContent: 'center'
    },
    seekBar: {
        flexGrow: 1
    },
    audioElement: {
        height: 0,
        width: 0,
    },
    viewStyle: {
        height: 250
    },
    videoElement: {
        position: 'absolute',
        top: 0,
        left: 0,
        bottom: 0,
        right: 0,
        height: 250,
    }
});
