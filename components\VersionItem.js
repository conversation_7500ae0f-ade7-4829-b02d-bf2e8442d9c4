import {Platform, StyleSheet, Text, View} from 'react-native';
import {Colors} from '../constants/styles';

function VersionItem({label, name, content}) {
  return (
    <View style={styles.container}>
      <View style={styles.labelContainer}>
        <Text style={[styles.label, styles.labelText]}>{label}</Text>
        <Text style={styles.labelText}>v{name}</Text>
      </View>
      <View style={styles.contentContainer}>
        <Text style={styles.contentText}>{content}</Text>
      </View>
    </View>
  );
}

export default VersionItem;

const styles = StyleSheet.create({
  container: {
    marginTop: 25
  },
  labelContainer: {
    flexDirection: 'row'
  },
  label: {
    flex: 1,
    color: Colors.accent100
  },
  labelText: {
    color: Colors.accent100,
    fontWeight: 'bold',
    fontSize: 12
  },
  contentContainer: {
    marginTop: 8,
    paddingHorizontal: 10,
    paddingVertical: 5,
    minHeight: 165,
    borderRadius: 8,
    elevation: 4,
    backgroundColor: 'white',
    shadowColor: 'black',
    shadowOpacity: 0.25,
    shadowOffset: {width: 0, height: 2},
    shadowRadius: 8,
    overflow: Platform.select({ios: 'visible', android: 'hidden'})
  },
  contentText: {
    color: Colors.accent100,
    fontSize: 12
  }
});
