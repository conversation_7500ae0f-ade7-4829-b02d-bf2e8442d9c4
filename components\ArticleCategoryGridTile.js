import GridTile from "./GridTile";
import {StyleSheet, Text} from "react-native";
import {Colors} from "../constants/styles";

function ArticleCategoryGridTile({categoryName, onPress, style}) {
    return (
        <GridTile onPress={onPress} style={{height: 160, ...style}}>
            <Text style={styles.categoryName}>{categoryName}</Text>
        </GridTile>
    );
}

export default ArticleCategoryGridTile;

const styles = StyleSheet.create({
    categoryName: {
        fontSize: 14,
        fontWeight: '600',
        color: Colors.accent100,
        textAlign: 'center',
        margin: 5
    }
});
