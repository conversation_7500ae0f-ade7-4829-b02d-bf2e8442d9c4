import {Pressable, StyleSheet, TextInput, View} from 'react-native';
import {Feather} from '@expo/vector-icons';
import {useState} from 'react';
import {Colors} from '../constants/styles';

function IconInput({placeholder, leftIconName, rightIconName, isSecure, onUpdateValue, isInvalid}) {
  const [enteredValue, setEnteredValue] = useState('');
  const [secure, setSecure] = useState(isSecure);
  const [rightIcon, setRightIcon] = useState(rightIconName);

  function onChangeTextHandler(enteredValue) {
    setEnteredValue(enteredValue);
    onUpdateValue(enteredValue);
  }

  function onRightIconPressHandler() {
    switch (rightIconName) {
      case 'x-circle':
        onChangeTextHandler('');
        break;
      case 'eye':
      case 'eye-off':
        const newSecure = !secure;
        setSecure(newSecure);
        setRightIcon(newSecure ? 'eye' : 'eye-off')
        break;
    }
  }

  return (
    <View style={[styles.container, isInvalid && styles.containerInvalid]}>
      <Feather style={styles.icon} name={leftIconName} size={20} color='#999999' />

      <TextInput value={enteredValue}
                 style={[styles.input, !enteredValue && styles.inputItalic]}
                 placeholder={placeholder}
                 secureTextEntry={secure}
                 autoCapitalize={'none'}
                 autoCorrect={false}
                 autoComplete={'off'}
                 textContentType='oneTimeCode'
                 onChangeText={onChangeTextHandler} />

      <Pressable onPress={onRightIconPressHandler}>
        <Feather style={styles.icon} name={rightIcon} size={20} color='#999999' />
      </Pressable>
    </View>
  );
}

export default IconInput;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    backgroundColor: 'white',
    marginVertical: 8,
    shadowRadius: 8,
    shadowColor: 'black',
    shadowOffset: {width: 0, height: 0},
    shadowOpacity: 0.35,
    elevation: 2
  },
  containerInvalid: {
    borderColor: Colors.error500,
    borderWidth: 1,
  },
  icon: {
    paddingVertical: 15,
    paddingHorizontal: 10
  },
  input: {
    flex: 1,
    color: Colors.accent100,
    fontSize: 18
  },
  inputItalic: {
    fontStyle: 'italic'
  }
});
