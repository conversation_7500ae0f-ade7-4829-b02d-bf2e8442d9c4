import {Pressable, StyleSheet, useWindowDimensions} from 'react-native';
import Checkbox from 'expo-checkbox';
import {Colors} from '../constants/styles';
import {RadioButton} from 'react-native-radio-buttons-group';
import RenderHtml from 'react-native-render-html';
import {newHtmlProps} from "../util/RenderHtmlRenfers";

function AnswerItem({
                        type,
                        index,
                        answerText,
                        selected,
                        onPress,
                        correct,
                        answeredCorrect,
                        isFinish,
                        isDisable
                    }) {
    let borderColor = selected ? Colors.primary800 : Colors.accent400;
    let backgroundColor;
    let textStyle;
    let checkboxColor = (selected && !isFinish) ? Colors.primary100 : undefined;

    const width = useWindowDimensions().width;

    let tagsStyles = {
        body: {
            fontSize: 15,
            lineHeight: 25,
            width: width - 95,
            textAlign: 'justify'
        },
        img: {
            maxWidth: width * 0.9,
            height: 'auto'
        },
        p: {
            marginVertical: 5
        },
        ol: {
            marginVertical: 5
        },
        iframe: {
            opacity: 0.99
        },
        // If you are using @native-html/table-plugin
        table: {
            opacity: 0.99
        }
    };

    if (isFinish) {
        if (selected) {
            borderColor = answeredCorrect ? Colors.primary800 : Colors.error500;
            if (answeredCorrect) {
                backgroundColor = Colors.primary900;
                textStyle = {
                    color: 'white',
                    fontWeight: 'bold'
                };
                tagsStyles.body = {...tagsStyles.body, color: 'white', fontWeight: 'bold'}
            }
        } else {
            borderColor = correct ? Colors.primary900 : Colors.accent400;
        }
    }

    const style = {
        borderColor: borderColor,
        backgroundColor: backgroundColor
    };

    if (type !== 'single') {
        return <Pressable key={index}
                          style={[styles.answerContainer, styles.checkboxAnswerContainer, style]}
                          onPress={() => onPress(index, type)}>
            <Checkbox style={styles.answerCheckbox}
                      color={checkboxColor}
                      disabled={isFinish || isDisable}
                      onValueChange={() => onPress(index, type)}
                      value={selected}>
            </Checkbox>
            <RenderHtml
                contentWidth={width}
                tagsStyles={tagsStyles}
                source={{html: answerText}}
                {...newHtmlProps}
            />
        </Pressable>;
    } else {
        return <Pressable key={index}
                          style={[styles.answerContainer, style]}
                          onPress={() => onPress(index, type)}>
            <RadioButton id={index}
                         labelStyle={[styles.answerText, textStyle]}
                         size={18}
                         color={checkboxColor}
                         borderColor={checkboxColor}
                         value={index}
                         onPress={(value) => onPress(value, type)}
                         selected={selected}/>
            <RenderHtml
                contentWidth={width}
                tagsStyles={tagsStyles}
                source={{html: answerText}}
                {...newHtmlProps}
            />
        </Pressable>;
    }
}

export default AnswerItem;

const styles = StyleSheet.create({
    answerContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 10,
        borderWidth: 2,
        borderRadius: 6,
        paddingVertical: 5
    },
    checkboxAnswerContainer: {
        paddingHorizontal: 10
    },
    answerCheckbox: {
        marginRight: 15,
        width: 18,
        height: 18
    },
    answerText: {
        flex: 1,
        flexWrap: 'wrap',
        lineHeight: 25,
        fontSize: 15
    }
});
