import axios from 'axios';
import {BE_URL} from '../constants/constants';
import CheckVersion from "../models/CheckVersion";

export async function checkVersion(versionName, buildNumber, os) {
  const url = BE_URL + "/api/version/check-update";
  const response = await axios.post(url, {
    "os": os,
    "versionName": versionName,
    "buildNumber": buildNumber
  });
  const data = response.data.data;
  return new CheckVersion(data.haveNewVersion, data.forceDownload, data.iosStoreUrl, data.androidStoreUrl);
}
