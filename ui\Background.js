import {StyleSheet, View} from 'react-native';

function Background({children}) {
  return (
    <View style={styles.container}>
      {/*<ImageBackground source={require("../assets/background.png")} resizeMode="cover" style={styles.image}>*/}
      {children}
      {/*</ImageBackground>*/}
    </View>
  );
}

export default Background;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor: Colors.primary100
  },
  image: {
    flex: 1,
    width: '100%',
    height: '100%'
  }
});
