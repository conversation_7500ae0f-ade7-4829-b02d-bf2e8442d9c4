import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>ie<PERSON>, StyleSheet, Text, View} from "react-native";
import {Colors} from "../constants/styles";
import Button from "../ui/Button";
import {BANNER_AD_UNIT_ID, SHOW_BANNER_AD} from "../constants/constants";
import {BannerAd, BannerAdSize} from "react-native-google-mobile-ads";
import React, {useContext, useState} from "react";
import {resetPass} from "../util/UserApi";
import LoadingOverlay from "../ui/LoadingOverlay";
import IconInput from "../ui/IconInput";
import ConfirmationInput from "../ui/ConfirmationInput";
import {generateVerification} from "../util/AuthApi";
import VerificationType from "../constants/VerificationType";
import {AuthContext} from "../store/AuthContext";
import validator from 'validator';
import {useNavigation} from "@react-navigation/native";

function ForgetPasswordScreen() {
    const [isAuthenticating, setIsAuthenticating] = useState(false);
    const [email, setEmail] = useState(null);
    const [confirmationCode, setConfirmationCode] = useState(null);
    const [newPassword, setNewPassword] = useState(null);
    const [confirmNewPassword, setConfirmNewPassword] = useState(null);
    const [newPassMatch, setNewPassMatch] = useState(true);
    const [enableButton, setEnableButton] = useState(false);

    const navigation = useNavigation();

    const authCtx = useContext(AuthContext);

    async function savePressHandler() {
        const emailAdd = email.trim();
        const code = confirmationCode.trim();
        const password = newPassword.trim();

        const emailIsValid = validator.isEmail(emailAdd);
        const passwordIsValid = password.length >= 8;

        if (!emailIsValid) {
            Alert.alert('Alert', 'Please enter a valid email address.');
            return;
        }

        if (!passwordIsValid) {
            Alert.alert('Alert', 'Please enter a password of at least 8 characters.');
            return;
        }

        if (!code) {
            Alert.alert('Alert', 'Please enter confirmation code.');
            return;
        }

        await onSubmit(emailAdd, password, code);
    }

    function checkEmail() {
        return email && validator.isEmail(email);
    }

    async function onSubmit(email, password, code) {
        try {
            setIsAuthenticating(true);
            const response = await resetPass(email, password, code);
            console.log(response);
            if (response.code === 200) {
                Alert.alert(
                    "Success",
                    "Account password has been reset successfully",
                    [{
                        text: "OK",
                        onPress: () => navigation.navigate('Login')
                    }]
                );
            } else {
                Alert.alert(
                    "Fail",
                    response.message,
                    [{
                        text: "OK"
                    }]
                );
            }
        } catch (e) {
            console.log(e);
            Alert.alert(
                "Fail",
                "An error occurred, please try again later",
                [{
                    text: "OK"
                }]
            );
        } finally {
            setIsAuthenticating(false);
        }
    }

    function updateData(value, type) {
        switch (type) {
            case "email":
                setEmail(value);
                checkData(value, confirmationCode, newPassword, confirmNewPassword);
                break;
            case "confirmationCode":
                setConfirmationCode(value);
                checkData(email, value, newPassword, confirmNewPassword);
                break;
            case "newPass":
                setNewPassword(value);
                checkData(email, confirmationCode, value, confirmNewPassword);
                break;
            case "confirmPass":
                setConfirmNewPassword(value);
                checkData(email, confirmationCode, newPassword, value);
                break;
        }
    }

    async function sendConfirmationCode() {
        try {
            const emailIsValid = checkEmail();

            if (!emailIsValid) {
                Alert.alert('Alert', 'Please enter a valid email address.');
            } else {
                await generateVerification(email.trim(), VerificationType.RESET_PASS, true);
                return true;
            }
        } catch (e) {
            console.log(e);
            Alert.alert(
                "Fail",
                "An error occurred, please try again later",
                [{
                    text: "OK"
                }]
            );
        }
        return false;
    }

    function checkData(email, confirmationCode, newPass, confirmPass) {
        const allFieldNotBlank = email && confirmationCode && newPass && confirmPass;

        const passMatch = (newPass === confirmPass) || !newPass || !confirmPass;
        setNewPassMatch(passMatch);

        setEnableButton(allFieldNotBlank && passMatch);
    }

    if (isAuthenticating) {
        return <LoadingOverlay message="Updating..."/>;
    }

    return (
        <>
            <View style={{flex: 1}}>
                <ScrollView keyboardShouldPersistTaps='handled' contentContainerStyle={styles.container}>
                    <IconInput placeholder='Email' leftIconName='user' rightIconName='x-circle'
                               onUpdateValue={(enteredValue) => updateData(enteredValue, "email")}/>

                    <ConfirmationInput
                        placeholder='Email confirmation code'
                        onSendCode={sendConfirmationCode}
                        disabled={!checkEmail()}
                        onUpdateValue={(enteredValue) => updateData(enteredValue, "confirmationCode")}/>

                    <IconInput placeholder='Password (at least 8 characters)' isSecure={true} leftIconName='lock'
                               rightIconName='eye'
                               onUpdateValue={(enteredValue) => updateData(enteredValue, "newPass")}/>

                    <IconInput placeholder='Confirm password' isSecure={true} leftIconName='lock' rightIconName='eye'
                               onUpdateValue={(enteredValue) => updateData(enteredValue, "confirmPass")}/>
                    {!newPassMatch && <Text style={styles.errorText}>Passwords do not match.</Text>}

                    <View style={styles.buttonContainer}>
                        <Button disable={!enableButton} style={styles.button} onPress={savePressHandler}>Update</Button>
                    </View>
                </ScrollView>
            </View>
            {SHOW_BANNER_AD && <BannerAd
                unitId={BANNER_AD_UNIT_ID}
                size={BannerAdSize.ANCHORED_ADAPTIVE_BANNER}
                requestOptions={{
                    requestNonPersonalizedAdsOnly: true,
                }}
            />}
        </>
    );
}

export default ForgetPasswordScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        marginHorizontal: 10,
        marginTop: 50
    },
    buttonContainer: {
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: 40
    },
    button: {
        width: 237,
        height: 50
    },
    errorText: {
        color: Colors.error500,
        marginTop: 10
    }
});
