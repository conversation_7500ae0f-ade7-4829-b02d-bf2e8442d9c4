import {useNavigation} from "@react-navigation/native";
import Purchases from "react-native-purchases";
import {Alert, Pressable, View, StyleSheet, Text} from "react-native";
import {Colors} from "../constants/styles";
import {useState} from "react";
import crashlytics from '@react-native-firebase/crashlytics';

function PackageItem({purchasePackage, setIsPurchasing, entitlement, disabled}) {
    const {
        product: {title, description, priceString},
    } = purchasePackage;

    const [selected, setSelected] = useState(false);
    const navigation = useNavigation();

    const onSelection = async () => {
        setIsPurchasing(true);
        setSelected(true);

        try {
            console.log(purchasePackage);
            const {customerInfo, productIdentifier} = await Purchases.purchasePackage(purchasePackage);
            console.info(customerInfo);
            console.info(productIdentifier);

            if (typeof customerInfo.entitlements.active[entitlement] !== 'undefined') {
                navigation.goBack();
            }
        } catch (e) {
            if (!e.userCancelled) {
                Alert.alert('Error purchasing package', e.message);
            }
            crashlytics().recordError(error);
        } finally {
            setIsPurchasing(false);
            setSelected(false);
        }
    };

    return (
        <Pressable disabled={disabled} onPress={onSelection}
                   style={[styles.container, selected && styles.containerSelected]}>
            <View style={styles.left}>
                {/*<Text style={styles.title}>{title}</Text>*/}
                <Text style={styles.terms}>{description}</Text>
            </View>
            <Text style={styles.price}>{priceString}</Text>
        </Pressable>
    );
}

export default PackageItem;

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 8,
        // backgroundColor: Colors.primary600,
        borderBottomWidth: 1,
        borderBottomColor: '#242424',
    },
    containerSelected: {
        backgroundColor: Colors.primary600,
        opacity: 0.7
    },
    price: {
        color: Colors.primary100,
        fontSize: 16,
        fontWeight: 'bold',
    },
    terms: {
        fontSize: 16,
        fontWeight: "500",
        paddingVertical: 5
    },
});
