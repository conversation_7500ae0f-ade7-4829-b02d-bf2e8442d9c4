import {createContext} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

export const QuizContext = createContext({
    saveQuiz: (quizId, quizProgressData) => {
    },
    deleteQuiz: (quizId) => {
    },
    loadQuiz: (quizId) => {
    },
    enableAutoPlay: () => {},
    disableAutoPlay: () => {},
    isAutoPlay: () => {}
});

function QuizProvider({children}) {

    async function saveQuiz(quizId, quizProgressData) {
        try {
            await AsyncStorage.setItem('quiz_data_' + quizId, JSON.stringify(quizProgressData, replacer));
        } catch (ex) {
            console.log(ex)
        }
    }

    async function deleteQuiz(quizId) {
        try {
            await AsyncStorage.removeItem('quiz_data_' + quizId);
        } catch (ex) {
            console.log(ex)
        }
    }

    async function loadQuiz(quizId) {
        try {
            const quizData = await AsyncStorage.getItem('quiz_data_' + quizId);
            if (quizData) {
                return JSON.parse(quizData, reviver);
            }
        } catch (ex) {
            console.log(ex)
        }
        return null;
    }

    async function enableAutoPlay() {
        try {
            await AsyncStorage.setItem('auto_play', "true");
        } catch (ex) {
            console.log(ex)
        }
    }

    async function disableAutoPlay() {
        try {
            await AsyncStorage.setItem('auto_play', "false");
        } catch (ex) {
            console.log(ex)
        }
    }

    async function isAutoPlay() {
        try {
            const autoPlay = await AsyncStorage.getItem('auto_play');
            return autoPlay !== "false";
        } catch (ex) {
            console.log(ex)
        }
        return true;
    }

    function replacer(key, value) {
        if(value instanceof Map) {
            return {
                dataType: 'Map',
                value: Array.from(value.entries()), // or with spread: value: [...value]
            };
        } else {
            return value;
        }
    }

    function reviver(key, value) {
        if(typeof value === 'object' && value !== null) {
            if (value.dataType === 'Map') {
                return new Map(value.value);
            }
        }
        return value;
    }

    const value = {
        saveQuiz: saveQuiz,
        loadQuiz: loadQuiz,
        deleteQuiz: deleteQuiz,
        enableAutoPlay: enableAutoPlay,
        disableAutoPlay: disableAutoPlay,
        isAutoPlay: isAutoPlay
    }

    return <QuizContext.Provider value={value}>{children}</QuizContext.Provider>
}

export default QuizProvider;
