import GridTile from './GridTile';
import {StyleSheet, Text, View} from 'react-native';
import {Colors} from '../constants/styles';
import {EvilIcons} from '@expo/vector-icons';

function TestGridTile({onPress, style, index, item}) {
  function indexToText(index) {
    const num = index + 1;
    return num < 10 ? ('0' + num) : num;
  }

  let textColor = {color: Colors.primary100};
  if (item.pass) {
    textColor = {color: Colors.primary800};
  }

  return (
    <GridTile onPress={onPress} style={{height: '100%', ...style}}>
      <View style={styles.itemContainer}>
        <View style={styles.numContainer}>
          <Text style={styles.numText}>{indexToText(index)}</Text>
        </View>
        <Text style={styles.title}>{item.title}</Text>
        <Text style={[styles.question, textColor]}>
          {item.answeredPoints ? item.answeredPoints : 0}/{item.totalPoints}
        </Text>
        <EvilIcons name="chevron-right" size={24} color="black" />
      </View>
      <View style={styles.timeContainer}>
        <Text style={styles.time}>{item.numQuestion} questions/{item.time} mins</Text>
      </View>
    </GridTile>
  );
}

export default TestGridTile;

const styles = StyleSheet.create({
  itemContainer: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10
  },
  timeContainer: {
    width: '100%',
    marginVertical: 5
  },
  numContainer: {
    borderColor: Colors.primary100,
    borderWidth: 1,
    width: 25,
    height: 25,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
    marginLeft: 8
  },
  numText: {
    color: Colors.primary100,
    fontSize: 14
  },
  title: {
    flex: 1,
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.accent100,
    marginRight: 10
  },
  question: {
    marginRight: 10
  },
  time: {
    fontStyle: 'italic',
    color: Colors.accent100,
    fontSize: 10,
    marginLeft: 50
  }
});
