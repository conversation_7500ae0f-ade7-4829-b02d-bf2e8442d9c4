import {useNavigation} from "@react-navigation/native";
import {
  I18nManager,
  Platform,
  SafeAreaView,
  StyleSheet,
  TouchableOpacity, useWindowDimensions,
  View
} from "react-native";
import AppIntroSlider from "react-native-app-intro-slider";
import {Colors} from "../constants/styles";
import {useContext, useRef} from "react";
import {AppContext} from "../store/AppContext";
import FastImage from "react-native-fast-image";

const isAndroidRTL = I18nManager.isRTL && Platform.OS === 'android';

function OnboardingScreen() {
  const navigation = useNavigation();
  const appCtx = useContext(AppContext);
  const slider = useRef();
  const height = useWindowDimensions().height;

  const imageButtonSize = height * 0.1;

  const slides = [
    {
      key: 1,
      image: require('../assets/onboarding/1.png'),
      pagination: require('../assets/onboarding/pagination/1.png')
    },
    {
      key: 2,
      image: require('../assets/onboarding/2.png'),
      pagination: require('../assets/onboarding/pagination/2.png')
    },
    {
      key: 3,
      image: require('../assets/onboarding/3.png'),
      pagination: require('../assets/onboarding/pagination/3.png')
    }
  ];

  function renderItems({item}) {
    return (
      <View style={[styles.containerOnboarding]}>
        <FastImage style={styles.imageOnboarding} resizeMode={FastImage.resizeMode.stretch}
                   source={item.image} />
      </View>
    );
  }

  function renderPagination(index) {
    const item = slides[index];
    return (
      <View style={[styles.paginationContainer, {bottom: imageButtonSize * 0.3}]}>
        <SafeAreaView>
          <View style={styles.paginationDots}>
            <TouchableOpacity key={index} onPress={() => paginationPress(index)}>
              <FastImage style={{width: imageButtonSize, height: imageButtonSize}} source={item.pagination} />
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </View>
    );
  }

  function paginationPress(index) {
    if (index < slides.length - 1) {
      slider.current.goToSlide(index + 1, true);
    } else {
      onDone();
    }
  }

  function onDone() {
    appCtx.finishOnboarding();
    navigation.replace('Register');
  }

  return <AppIntroSlider activeDotStyle={{backgroundColor: Colors.primary100}}
                         renderItem={renderItems}
                         renderPagination={renderPagination}
                         data={slides}
                         ref={(ref) => (slider.current = ref)} />;
}

export default OnboardingScreen;

const styles = StyleSheet.create({
  containerOnboarding: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center'
  },
  imageOnboarding: {
    flex: 1,
    width: '100%',
    height: '100%'
  },
  paginationContainer: {
    position: 'absolute',
    left: 16,
    right: 16,
    justifyContent: 'center',
  },
  paginationDots: {
    flexDirection: isAndroidRTL ? 'row-reverse' : 'row',
    justifyContent: 'center',
    alignItems: 'center'
  }
});
