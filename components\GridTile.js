import {Platform, Pressable, StyleSheet, View} from 'react-native';
import {Colors} from '../constants/styles';

function GridTile({children, onPress, style}) {
  return (
    <View style={[styles.gridItem, style]}>
      <Pressable android_ripple={{color: Colors.accent200}}
                 style={({pressed}) => [styles.button, pressed ? styles.buttonPressed : null]}
                 onPress={onPress}>
        <View style={styles.innerContainer}>
          {children}
        </View>
      </Pressable>
    </View>
  );
}

export default GridTile;

const styles = StyleSheet.create({
  gridItem: {
    flex: 1,
    marginTop: 2,
    marginBottom: 10,
    borderRadius: 8,
    elevation: 4,
    backgroundColor: 'white',
    shadowColor: 'black',
    shadowOpacity: 0.25,
    shadowOffset: {width: 0, height: 2},
    shadowRadius: 8,
    overflow: Platform.select({ios: 'visible', android: 'hidden'})
  },
  button: {
    flex: 1
  },
  buttonPressed: {
    opacity: 0.5
  },
  innerContainer: {
    flex: 1,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center'
  }
});
