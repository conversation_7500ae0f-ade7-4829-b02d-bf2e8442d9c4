import {useEffect, useState} from "react";
import Purchases from "react-native-purchases";
import {<PERSON><PERSON>, FlatList, View, StyleSheet, Text, TouchableOpacity, Linking, Platform} from "react-native";
import PackageItem from "../components/PackageItem";
import LoadingOverlay from "../ui/LoadingOverlay";
import {PRIVACY_POLICY, TERMS_AND_CONDITIONS} from "../constants/constants";

function PaywallScreen({route}) {
  const offer = route.params.offer;
  const entitlement = route.params.entitlement;
  const title = route.params.title;

  const [packages, setPackages] = useState([]);
  const [isPurchasing, setIsPurchasing] = useState(false);

  useEffect(() => {
    const getPackages = async () => {
      try {
        const offerings = await Purchases.getOfferings();
        console.log(offer);
        console.log(offerings.all[offer]);

        if (offerings.all[offer]
            && offerings.all[offer].availablePackages.length !== 0) {
          console.log(offerings.all[offer].availablePackages);
          setPackages(offerings.all[offer].availablePackages);
        }
      } catch (e) {
        console.error(e);
        Alert.alert('Error getting offers', e.message);
      }
    };

    getPackages();
  }, []);

  const header = () => <Text style={styles.title}>{title}</Text>;

  const footer = () => {
    return (
      <View style={{marginTop: 10}}>
        {Platform.OS === 'ios' && <Text style={styles.text}>
          The purchase will be applied to your iTunes account on confirmation.
          Subscriptions do not auto-renew, so at the end of the current period you will need to purchase again
          to access the features.
        </Text>}
        {Platform.OS === 'android' && <Text style={styles.text}>
          The purchase will be applied to your Google account on confirmation.
          Subscriptions will <Text style={{fontWeight: 'bold'}}>automatically renew</Text> unless canceled
          within 24-hours before the end of the current period.
          You can cancel anytime with your Play Store settings.
        </Text>}
        <Text style={[styles.text, {marginTop: 5}]}>For more information, please see in our links:</Text>
        <View style={{alignItems: "center", marginTop: 10}}>
          <TouchableOpacity onPress={() => Linking.openURL(TERMS_AND_CONDITIONS)}>
            <Text style={{color: 'blue', fontSize: 16}}>
              Term of Service
            </Text>
          </TouchableOpacity>
        </View>
        <View style={{alignItems: "center", marginTop: 10}}>
          <TouchableOpacity onPress={() => Linking.openURL(PRIVACY_POLICY)}>
            <Text style={{color: 'blue', fontSize: 16}}>
              Privacy Policy
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.page}>
      <FlatList
        data={packages}
        renderItem={({item}) => <PackageItem purchasePackage={item}
                                             disabled={isPurchasing}
                                             setIsPurchasing={setIsPurchasing}
                                             entitlement={entitlement} />}
        keyExtractor={(item) => item.identifier}
        ListHeaderComponent={header}
        ListHeaderComponentStyle={styles.headerFooterContainer}
        ListFooterComponent={footer}
        ListFooterComponentStyle={styles.headerFooterContainer}
      />

      {isPurchasing && <LoadingOverlay message={'Paying...'} />}
    </View>
  );
}

export default PaywallScreen;

const styles = StyleSheet.create({
  page: {
    padding: 16,
  },
  text: {
    color: 'black',
    fontSize: 16,
    lineHeight: 22,
    textAlign: 'justify'
  },
  title: {
    color: 'black',
    fontWeight: "bold",
    fontSize: 18,
    textAlign: "center"
  },
  headerFooterContainer: {
    marginVertical: 10,
  }
});
