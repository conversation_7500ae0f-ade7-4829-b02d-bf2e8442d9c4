import {FlatList, Platform, StyleSheet, Text, View} from 'react-native';
import {VERSIONS} from '../data/DummyData';
import VersionItem from '../components/VersionItem';
import Button from '../ui/Button';
import {Colors} from '../constants/styles';
import {useEffect, useState} from 'react';
import {MaterialIcons} from '@expo/vector-icons';
import {expo} from '../app.json'

function SoftwareInfoScreen() {
  const [isLatest, setIsLatest] = useState(true);

  useEffect(() => {
    setIsLatest(VERSIONS[0].code === expo.version);
  }, [VERSIONS, expo, setIsLatest]);

  const buttonStyle = isLatest ? {
    backgroundColor: Colors.accent100,
    opacity: 0.3
  } : {backgroundColor: Colors.primary100}

  function updatePressHandler() {

  }

  function renderItem(itemData) {
    return <VersionItem
      name={itemData.item.code}
      content={itemData.item.content}
      label={itemData.item.isLatest ? 'Latest version' : (itemData.item.isCurrent ? 'Current version' : '')} />;
  }

  return (
    <View style={styles.container}>
      {!isLatest && <FlatList
        style={styles.versionList}
        data={VERSIONS}
        keyExtractor={(item) => item.code}
        renderItem={renderItem}
        numColumns={1} />}

      {isLatest && <View>
        {renderItem({item: VERSIONS[0]})}

        <View style={styles.verifiedContainer}>
          <MaterialIcons name="verified" size={24} color={Colors.accent300} />
          <Text style={styles.verifiedText}>You are in the latest version!</Text>
        </View>
      </View>
      }

      <View style={styles.buttonContainer}>
        <Button style={[styles.button, buttonStyle]}
                disable={isLatest}
                onPress={updatePressHandler}>Update version</Button>
      </View>
    </View>
  );
}

export default SoftwareInfoScreen;

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 10,
    height: '100%'
  },
  versionList: {
    height: '70%'
  },
  buttonContainer: {
    height: '30%',
    justifyContent: 'center',
    alignItems: 'center'
  },
  button: {
    width: 237,
    height: 50
  },
  verifiedContainer: {
    marginTop: 100,
    backgroundColor: Colors.primary800 + '30',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    height: 34,
    borderRadius: 8,
    borderColor: Colors.primary800,
    borderWidth: 1,
    elevation: 4,
    shadowColor: 'black',
    shadowOpacity: 0.25,
    shadowOffset: {width: 0, height: 2},
    shadowRadius: 8,
    overflow: Platform.select({ios: 'visible', android: 'hidden'})
  },
  verifiedText: {
    fontSize: 14,
    color: Colors.accent300,
    marginLeft: 10
  }
});
