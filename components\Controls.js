import React from 'react';

import {View, StyleSheet, TouchableOpacity} from 'react-native';
import {Colors} from "../constants/styles";
import {Feather} from "@expo/vector-icons";

function Controls({paused, onPressPlay, onPressPause}) {
    return (
        <View style={styles.container}>
            {!paused ?
                <TouchableOpacity onPress={onPressPause}>
                    <Feather name='pause-circle' size={24} color={Colors.accent100}/>
                </TouchableOpacity> :
                <TouchableOpacity onPress={onPressPlay}>
                    <Feather name='play-circle' size={24} color={Colors.accent100}/>
                </TouchableOpacity>
            }
        </View>
    );
}

export default Controls;

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingTop: 8,
    }
})
