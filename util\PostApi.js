import axios from "axios";
import {BE_URL} from "../constants/constants";
import ArticleSpace from "../models/ArticleSpace";
import ArticleCategory from "../models/ArticleCategory";
import Article from "../models/Article";

export async function getArticleSpaces(authCtx, language) {
    try {
        const headers = {Authorization: `Bearer ${authCtx.token}`};
        const response = await axios.get(BE_URL + "/api/post/space?language=" + language, {headers});

        const data = [];
        for (const item of response.data.data) {
            const categories = [];
            for (const category of item.categories) {
                categories.push(new ArticleCategory(category.id, category.name));
            }

            data.push(new ArticleSpace(item.id, item.title, categories));
        }
        return data;
    } catch (e) {
        handleException(e, authCtx);
    }
    return [];
}

export async function getPosts(authCtx, categoryId) {
    try {
        const headers = {Authorization: `Bearer ${authCtx.token}`};
        const response = await axios.get(BE_URL + "/api/post?categoryId=" + categoryId, {headers});

        const data = [];
        for (const item of response.data.data) {
            data.push(new Article(item.id, item.title, item.content));
        }
        return data;
    } catch (e) {
        handleException(e, authCtx);
    }
    return [];
}

export async function getPostInfo(authCtx, postId) {
    try {
        const headers = {Authorization: `Bearer ${authCtx.token}`};
        const response = await axios.get(BE_URL + "/api/post/" + postId, {headers});

        const item = response.data.data;
        return new Article(item.id, item.title, item.content);
    } catch (e) {
        handleException(e, authCtx);
    }
    return new Article();
}

function handleException(e, authCtx) {
    console.error(e);
    if (e.response.status === 401) {
        authCtx.logout();
    }
}
