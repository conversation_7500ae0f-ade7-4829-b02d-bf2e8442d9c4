{"expo": {"name": "<PERSON><PERSON><PERSON>", "slug": "<PERSON><PERSON><PERSON>", "owner": "hienhv", "version": "1.5.6", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "updates": {"fallbackToCacheTimeout": 0}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.hth.udecareer", "buildNumber": "1.16", "googleServicesFile": "./assets/config/GoogleService-Info.plist"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FFFFFF"}, "package": "com.hth.udecareer", "versionCode": 21, "googleServicesFile": "./assets/config/google-services.json"}, "web": {"favicon": "./assets/favicon.png"}, "extra": {"eas": {"projectId": "9e7b26ff-69e4-43bc-8264-46858f85392a"}}, "plugins": [["expo-build-properties", {"ios": {"useFrameworks": "static"}, "android": {"extraProguardRules": "-keep class com.google.android.gms.internal.consent_sdk.** { *; }", "compileSdkVersion": 34, "targetSdkVersion": 34, "buildToolsVersion": "33.0.0"}}], ["expo-tracking-transparency", {"userTrackingPermission": "This identifier will be used to deliver personalized ads to you."}], ["@react-native-firebase/app"], ["@react-native-firebase/crashlytics"]]}, "react-native-google-mobile-ads": {"android_app_id": "ca-app-pub-8250110077262376~2692295688", "ios_app_id": "ca-app-pub-8250110077262376~1187642323", "delay_app_measurement_init": true, "user_tracking_usage_description": "This identifier will be used to deliver personalized ads to you."}}