import {Button, TextInput, View, StyleSheet} from "react-native";
import {Colors} from "../constants/styles";
import {Feather} from "@expo/vector-icons";
import {useState} from "react";

function ConfirmationInput({isInvalid, placeholder, onUpdateValue, onSendCode, disabled}) {
  const [enteredValue, setEnteredValue] = useState('');
  const [timer, setTimer] = useState(null);

  function onChangeTextHandler(enteredValue) {
    setEnteredValue(enteredValue);
    onUpdateValue(enteredValue);
  }

  async function onSendCodeHandler() {
    if (await onSendCode()) {
      setTimer(60);

      let interval = setInterval(() => {
        setTimer(lastTimerCount => {
          lastTimerCount <= 1 && clearInterval(interval)
          return lastTimerCount - 1
        })
      }, 1000);
    }
  }

  return (
    <>
      <View style={[styles.container, isInvalid && styles.containerInvalid]}>
        <Feather style={styles.icon} name="key" size={20} color='#999999' />

        <TextInput style={styles.input}
                   value={enteredValue}
                   placeholder={placeholder}
                   autoCapitalize={'none'}
                   autoCorrect={false}
                   autoComplete={'off'}
                   textContentType='oneTimeCode'
                   onChangeText={onChangeTextHandler} />
        <Button title={timer ? timer + "" : 'Send code'} disabled={disabled} onPress={onSendCodeHandler} />
      </View>
    </>
  )
}

export default ConfirmationInput;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    backgroundColor: 'white',
    marginVertical: 8,
    shadowRadius: 8,
    shadowColor: 'black',
    shadowOffset: {width: 0, height: 0},
    shadowOpacity: 0.35,
    elevation: 2
  },
  containerInvalid: {
    borderColor: Colors.error500,
    borderWidth: 1,
  },
  icon: {
    paddingVertical: 15,
    paddingHorizontal: 10
  },
  input: {
    flex: 1,
    height: 55,
    fontSize: 18,
    fontStyle: 'italic',
    color: Colors.accent100,
  }
});
