import {Al<PERSON>, Platform, ScrollView, StyleSheet, Text, useWindowDimensions, View} from 'react-native';
import {useContext, useEffect, useLayoutEffect, useState, useRef} from 'react';
import {getQuestions, submitAnswerData} from '../util/QuizApi';
import {AuthContext} from '../store/AuthContext';
import LoadingOverlay from '../ui/LoadingOverlay';
import ResultForm from '../components/ResultForm';
import TestDetailHeader from '../components/TestDetailHeader';
import TestDetailFooter from '../components/TestDetailFooter';
import TestDetailBody from '../components/TestDetailBody';
import TestDescriptionForm from '../components/TestDescriptionForm';
import {Colors} from '../constants/styles';
import TestStatus from '../constants/TestStatus';
import {BANNER_AD_UNIT_ID, INTERSTITIAL_AD_UNIT_ID, SHOW_BANNER_AD} from '../constants/constants';
import {AdEventType, BannerAd, BannerAdSize, InterstitialAd} from 'react-native-google-mobile-ads';
import RenderHtml from 'react-native-render-html';
import {newHtmlProps} from '../util/RenderHtmlRenfers';
import {QuizContext} from "../store/QuizContext";

function TestDetailScreen({navigation, route}) {
  const [isLoading, setIsLoading] = useState(true);
  const [displayedTest, setDisplayedTest] = useState(null);
  const [currQuestionIdx, setCurrQuestionIdx] = useState(0);
  const [answeredMap, setAnsweredMap] = useState(new Map());
  const [questionNumber, setQuestionNumber] = useState([]);
  const [startTime, setStartTime] = useState(Math.floor(Date.now() / 1000));
  const [resultResponse, setResultResponse] = useState(null);
  const [testStatus, setTestStatus] = useState(TestStatus.WAITING);
  const [haveSavedTest, setHaveSavedTest] = useState(false);
  const [testTime, setTestTime] = useState({
    totalTime: 0,
    remainTime: 0,
    countDown: 0,
    totalElapsedTime: 0,
    groupElapsedTime: 0,
  });

  const width = useWindowDimensions().width;
  const testingScrollViewRef = useRef(null);
  let scrollIndex = 0;
  const quizCtx = useContext(QuizContext);

  const tagsStyles = {
    body: {
      fontSize: 16,
      lineHeight: 25,
      textAlign: 'justify'
    },
    img: {
      maxWidth: width * 0.9,
      height: 'auto'
    },
    p: {
      marginVertical: 5
    },
    ol: {
      marginVertical: 5
    },
    iframe: {
      opacity: 0.99
    },
    // If you are using @native-html/table-plugin
    table: {
      opacity: 0.99
    }
  };

  const testId = route.params.testId;
  const catTitle = route.params.categoryTitle;
  const catHeader = route.params.categoryHeader;
  const typeTestTitle = route.params.typeTestTitle;
  const isPurchased = route.params.isPurchased;

  const authCtx = useContext(AuthContext);
  const [interstitialAd, setInterstitialAd] = useState(null);

  useLayoutEffect(() => {
    navigation.setOptions({
      title: (catHeader ? catHeader : catTitle) + ': ' + typeTestTitle
    });
  }, [catHeader, catTitle, typeTestTitle, navigation]);

  useEffect(() => {
    quizCtx.loadQuiz(testId).then((savedTest) => {
      if (savedTest) {
        setHaveSavedTest(true);
      }
    })

    quizCtx.enableAutoPlay();

    getQuizInfo();
  }, []);

  useEffect(() => {
    return navigation.addListener('beforeRemove', (e) => {
      if (testStatus === TestStatus.WAITING) {
        // If we don't have unsaved changes, then we don't need to do anything
        return;
      }

      // Prevent default behavior of leaving the screen
      e.preventDefault();

      if (currQuestionIdx > 0 && testStatus === TestStatus.STARTED) {
        quizCtx.saveQuiz(displayedTest.id, {
          currQuestionIdx: currQuestionIdx,
          testTime: testTime,
          startTime: startTime,
          testData: displayedTest,
          answeredMap: answeredMap,
          questionNumber: questionNumber
        })
      }

      const message = testStatus === TestStatus.STARTED
                      ? "You have unfinished test. Are you sure to discard them and leave the screen?"
                      : "Are you sure you want to leave? Data will not be saved!"
      const title = testStatus === TestStatus.STARTED ? "Discard tests" : "Leave screen"

      // Prompt the user before leaving the screen
      Alert.alert(
        title,
        message,
        [
          {
            text: "Cancel",
            style: 'cancel'
          },
          {
            text: 'Agree',
            style: 'destructive',
            // If the user confirmed, then we dispatch the action we blocked earlier
            // This will continue the action that had triggered the removal of the screen
            onPress: () => navigation.dispatch(e.data.action),
          },
        ]
      );
    });
  }, [navigation, testStatus, currQuestionIdx]);

  useEffect(() => {
    if (!isPurchased) {
      const interstitialAd = InterstitialAd.createForAdRequest(INTERSTITIAL_AD_UNIT_ID, {
        requestNonPersonalizedAdsOnly: true
      });
      setInterstitialAd(interstitialAd);

      const subscriptions = [
        interstitialAd.addAdEventListener(AdEventType.LOADED, () => {
          console.log('InterstitialAd adLoaded {}', interstitialAd.loaded);
        }),
        interstitialAd.addAdEventListener(AdEventType.ERROR, () => {
          console.warn('InterstitialAd => Error');
        }),
        interstitialAd.addAdEventListener(AdEventType.OPENED, () => {
          console.log('InterstitialAd => adOpened');
        }),
        interstitialAd.addAdEventListener(AdEventType.CLICKED, () => {
          console.log('InterstitialAd => adClicked');
        }),
        interstitialAd.addAdEventListener(AdEventType.CLOSED, () => {
          console.log('InterstitialAd => adClosed');
          interstitialAd.load();
        }),
      ];

      // Start loading the interstitial straight away
      interstitialAd.load();

      return () => subscriptions.forEach((sub) => sub == null);
    }
  }, [isPurchased]);

  async function getQuizInfo() {
    setIsLoading(true);
    try {
      const quizData = await getQuestions(authCtx, testId);
      setDisplayedTest(quizData);

      setTestTime({
        totalTime: quizData.time,
        remainTime: quizData.time,
        countDown: quizData.time,
        groupElapsedTime: 0,
        totalElapsedTime: 0
      });

      const newQuestionNumber = [];
      const newAnswerMap = new Map();
      for (let i = 0; i < quizData.questions.length; i++) {
        newQuestionNumber.push({
          index: i,
          current: i === 0,
          answered: false,
          reviewed: false
        });
        newAnswerMap.set(quizData.questions[i].id, []);
      }
      setQuestionNumber(newQuestionNumber);
      setAnsweredMap(newAnswerMap);
      setCurrQuestionIdx(0);
    } catch (e) {
      console.log(e);
    } finally {
      setIsLoading(false);
    }
  }

  function startTestHandler() {
    if (displayedTest) {
      setTestStatus(TestStatus.STARTED);
      nextQuestion(0);
      setStartTime(Math.floor(Date.now() / 1000));
      setResultResponse(null);
      quizCtx.deleteQuiz(testId);
      setHaveSavedTest(false);
    }
  }

  async function resumeTestHandler() {
    setIsLoading(true);
    const savedQuizData = await quizCtx.loadQuiz(testId);

    if (savedQuizData) {
      const savedTestTime = savedQuizData.testTime;
      savedTestTime.remainTime = savedTestTime.countDown;
      setTestTime(savedTestTime);

      setDisplayedTest(savedQuizData.testData);

      setAnsweredMap(savedQuizData.answeredMap);
      setStartTime(savedQuizData.startTime);
      setResultResponse(null);

      nextQuestion(savedQuizData.currQuestionIdx);
      setQuestionNumber(savedQuizData.questionNumber);

      setTimeout(() => {
        setTestStatus(TestStatus.STARTED);
        setIsLoading(false);
      }, 1000);
    }
  }

  function nextQuestion(questionIdx) {
    setCurrQuestionIdx(questionIdx);
    setQuestionNumberState(questionIdx, "current");

    if (scrollIndex > 0) {
      testingScrollViewRef.current?.scrollTo({x: 0, animated: false});
    }

    if (questionIdx > 0 && testStatus === TestStatus.STARTED) {
      quizCtx.saveQuiz(displayedTest.id, {
        currQuestionIdx: questionIdx,
        questionNumber: questionNumber,
        testTime: testTime,
        startTime: startTime,
        testData: displayedTest,
        answeredMap: answeredMap
      })
    }
  }

  function setQuestionNumberState(questionIdx, type, value, correct) {
    const newQuestionNumber = [...questionNumber];
    for (const x of newQuestionNumber) {
      if (type === "current") {
        x.current = false;
      }
      if (x.index === questionIdx) {
        switch (type) {
          case "answered":
            x.answered = value;
            x.correct = correct;
            break;
          case "review":
            x.reviewed = !x.reviewed;
            break;
          case "current":
            x.current = true;
            break;
        }
      }
    }
    setQuestionNumber(newQuestionNumber);
  }

  function updateAnswer(index, type) {
    if (!testStatus === TestStatus.STARTED) {
      return;
    }
    const selectedArr = [], correctArr = [];
    for (const x of displayedTest.questions[currQuestionIdx].answerData) {
      if (x.index === index) {
        x.selected = !x.selected;
      } else if (type === 'single') {
        x.selected = false;
      }

      x.answeredCorrect = x.correct && x.selected;
      selectedArr[x.index] = x.selected;
      correctArr[x.index] = x.correct;
    }
    const correct = selectedArr.toString() === correctArr.toString();
    displayedTest.questions[currQuestionIdx].correct = correct;

    answeredMap.set(displayedTest.questions[currQuestionIdx].id, selectedArr);
    setAnsweredMap(answeredMap);

    setQuestionNumberState(currQuestionIdx, "answered", selectedArr.find(x => x === true), correct);
  }

  function confirmSubmitHandler() {
    Alert.alert(
      "Finish test",
      "Are you sure you want to finish now?",
      [{
        text: "Cancel",
        style: 'cancel'
      }, {
        text: "Agree",
        style: 'destructive',
        onPress: () => submitAnswer()
      },
      ]
    );
  }

  function submitSuccessHandler() {
    quizCtx.deleteQuiz(displayedTest.id);
    setHaveSavedTest(false);
    if (interstitialAd === null || !interstitialAd.loaded) {
      return;
    }
    Alert.alert(
      "Success",
      "Detailed results will be displayed after the advertisement",
      [{
        text: "OK",
        onPress: () => {
          if (interstitialAd.loaded) {
            interstitialAd.show();
          }
        }
      }
      ],
      {cancelable: false}
    );
  }

  async function submitAnswer() {
    setIsLoading(true);
    try {
      quizCtx.disableAutoPlay();
      console.log(answeredMap);
      const elapsedTime = testTime.totalElapsedTime + testTime.groupElapsedTime;
      console.log("elapsedTime: " + elapsedTime);
      const resultData = await submitAnswerData(authCtx.token, testId, answeredMap, startTime,
        Math.floor(Date.now() / 1000), elapsedTime);
      setResultResponse(resultData);
      setTestStatus(TestStatus.FINISHED);
      nextQuestion(0);
      submitSuccessHandler();
    } catch (e) {
      console.log(e);
      Alert.alert("Error", "An error occurred, please try again later");
    } finally {
      setIsLoading(false);
    }
  }

  function timeUpHandler() {
    Alert.alert(
      "Time Up",
      "The exam time has ended, your answer will be recorded after you click OK",
      [{
        text: "OK",
        onPress: () => submitAnswer()
      }],
      {cancelable: false}
    );
  }

  function updateRemainTimeHandler(value) {
    testTime.countDown = value;
    testTime.groupElapsedTime = testTime.totalTime - value;
  }

  function restartQuizHandler() {
    setTestStatus(TestStatus.WAITING);
    getQuizInfo();
  }

  function showExplain() {
    return displayedTest.showExplain
           && displayedTest.showExplain > 0
           && (!!displayedTest.questions[currQuestionIdx].correctMsg ||
               !!displayedTest.questions[currQuestionIdx].incorrectMsg)
  }

  function handleScroll(event) {
    scrollIndex = event.nativeEvent.contentOffset.y;
  }

  function testingForm() {
    return (
      <>
        <TestDetailHeader totalTime={testTime.totalTime}
                          remainTime={testTime.remainTime}
                          isFinish={false}
                          onTimeUp={timeUpHandler}
                          updateRemainTime={updateRemainTimeHandler}
                          questionNumberData={questionNumber}
                          questionNumberPress={nextQuestion} />

        <View style={{
          borderColor: Colors.primary100,
          borderWidth: 1,
          paddingHorizontal: 5,
          marginTop: 5,
          backgroundColor: 'white',
          flex: 1,
          paddingVertical: 5
        }}>
          <ScrollView
              ref={testingScrollViewRef}
              onScroll={handleScroll}
              scrollEventThrottle={160}>
            <TestDetailBody currQuestionIdx={currQuestionIdx}
                            totalQuestion={displayedTest.questions.length}
                            questionData={displayedTest.questions[currQuestionIdx]}
                            reviewOnPress={() => setQuestionNumberState(currQuestionIdx, "review")}
                            isFinish={false}
                            isDisable={testStatus === TestStatus.TIME_UP}
                            updateAnswerHandler={updateAnswer} />
          </ScrollView>
        </View>

        <View>
          <TestDetailFooter currQuestionIdx={currQuestionIdx}
                            totalQuestions={displayedTest.questions.length}
                            nextPress={() => nextQuestion(currQuestionIdx + 1)}
                            finishPress={confirmSubmitHandler}
                            backPress={() => nextQuestion(currQuestionIdx - 1)} />
        </View>
      </>
    );
  }

  function finishedForm() {
    if (!resultResponse) {
      return;
    }
    return <ResultForm answeredTime={resultResponse.answeredTime}
                       passPercentage={resultResponse.passPercentage}
                       percentage={resultResponse.percentage}
                       pass={resultResponse.pass}
                       resultText={resultResponse.resultText}
                       corrects={resultResponse.corrects}
                       totalQuestion={resultResponse.questions}
                       totalPoint={resultResponse.totalPoint}
                       point={resultResponse.point}
                       viewQuestionPress={() => {
                         setTestStatus(TestStatus.VIEW_RESULT);
                         setIsLoading(true);
                         setTimeout(() => {
                           nextQuestion(0);
                           setIsLoading(false);
                         }, 1000);
                       }}
                       categoryName={catTitle}
                       restartQuizPress={restartQuizHandler} />;

  }

  function showQuestionResultForm() {
    if (!resultResponse) {
      return;
    }
    return (
      <>
        <TestDetailHeader totalTime={testTime.totalTime}
                          isFinish={true}
                          questionNumberData={questionNumber}
                          questionNumberPress={nextQuestion} />

        <View style={{
          borderColor: Colors.primary100,
          borderWidth: 1,
          paddingHorizontal: 5,
          marginTop: 5,
          backgroundColor: 'white',
          flex: 1,
          paddingVertical: 5
        }}>
          <ScrollView
              ref={testingScrollViewRef}
              onScroll={handleScroll}
              scrollEventThrottle={160}>
            <TestDetailBody currQuestionIdx={currQuestionIdx}
                            isFinish={true}
                            totalQuestion={displayedTest.questions.length}
                            questionData={displayedTest.questions[currQuestionIdx]}
                            updateAnswerHandler={() => {
                            }} />

            {showExplain() &&
             <View>
               <Text style={{textAlign: 'center', marginTop: 15}}>************************</Text>
               <Text style={styles.titleExplain}>Detailed explanation</Text>

               {displayedTest.questions[currQuestionIdx].correct &&
                <RenderHtml
                  contentWidth={width}
                  tagsStyles={tagsStyles}
                  source={{html: displayedTest.questions[currQuestionIdx].correctMsg}}
                  {...newHtmlProps}
                />
               }

               {!displayedTest.questions[currQuestionIdx].correct &&
                <RenderHtml
                  contentWidth={width}
                  tagsStyles={tagsStyles}
                  source={{html: displayedTest.questions[currQuestionIdx].incorrectMsg}}
                  {...newHtmlProps}
                />
               }
             </View>}
          </ScrollView>
        </View>

        <View>
          <TestDetailFooter currQuestionIdx={currQuestionIdx}
                            isFinish={true}
                            totalQuestions={displayedTest.questions.length}
                            nextPress={() => nextQuestion(currQuestionIdx + 1)}
                            finishPress={confirmSubmitHandler}
                            backPress={() => nextQuestion(currQuestionIdx - 1)} />
        </View>
      </>
    );
  }

  function testDetailForm() {
    switch (testStatus) {
      case TestStatus.STARTED:
      case TestStatus.TIME_UP:
        return testingForm();
      case TestStatus.FINISHED:
        return finishedForm();
      case TestStatus.VIEW_RESULT:
        return showQuestionResultForm();
      default:
        return <TestDescriptionForm title={displayedTest.title}
                                    description={displayedTest.description}
                                    haveSavedTest={haveSavedTest}
                                    resumeTestOnPress={resumeTestHandler}
                                    startTestOnPress={startTestHandler} />;
    }
  }

  if (isLoading) {
    return <LoadingOverlay message={'Loading...'} />;
  }

  return (
    <>
      <View style={styles.container}>
        <View style={styles.titleOuterContainer}>
          <View style={styles.titleInnerContainer}>
            <Text style={styles.title}>{displayedTest.title}</Text>
          </View>
        </View>

        {testDetailForm()}

        {(!SHOW_BANNER_AD || isPurchased) && <Text style={{height: 20}}></Text>}
      </View>

      {SHOW_BANNER_AD && !isPurchased &&
       <BannerAd
         unitId={BANNER_AD_UNIT_ID}
         size={BannerAdSize.ANCHORED_ADAPTIVE_BANNER}
         requestOptions={{
           requestNonPersonalizedAdsOnly: true,
         }}
       />}
    </>
  );
}

export default TestDetailScreen;

const styles = StyleSheet.create({
  container: {
    marginTop: 5,
    flex: 1,
    marginHorizontal: 15,
    justifyContent: 'space-between',
    flexGrow: 1,
    flexDirection: 'column'
  },
  titleOuterContainer: {
    minHeight: 57,
    backgroundColor: Colors.primary100,
    marginVertical: 5,
    borderRadius: 8,
    elevation: 2,
    borderColor: Colors.primary100,
    borderBottomWidth: 2,
    shadowColor: 'black',
    shadowOpacity: 0.15,
    shadowOffset: {width: 0, height: 2},
    shadowRadius: 8,
    overflow: Platform.select({ios: 'visible', android: 'hidden'})
  },
  titleInnerContainer: {
    minHeight: 55,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    paddingHorizontal: 3,
    paddingVertical: 6,
    elevation: 2,
    shadowColor: 'black',
    shadowOpacity: 0.15,
    shadowOffset: {width: 0, height: 2},
    shadowRadius: 8,
    overflow: Platform.select({ios: 'visible', android: 'hidden'})
  },
  title: {
    color: Colors.accent100,
    fontWeight: 'bold',
    fontSize: 16
  },
  titleExplain: {
    fontWeight: 'bold',
    fontSize: 22,
    textAlign: "center",
    marginBottom: 10
  }
});
