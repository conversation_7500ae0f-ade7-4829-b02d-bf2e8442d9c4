import Background from '../ui/Background';
import CategoryGridTile from '../components/CategoryGridTile';
import {Alert, FlatList, Linking, Platform, RefreshControl} from 'react-native';
import {useContext, useEffect, useState} from 'react';
import {getQuizCategoryList} from '../util/QuizApi';
import {AuthContext} from '../store/AuthContext';
import {BannerAd, BannerAdSize} from 'react-native-google-mobile-ads';
import {
    BANNER_AD_UNIT_ID, ENABLE_REVENUE_CAT,
    SHOW_BANNER_AD,
    TYPE_TEST_FULL_CODE,
    TYPE_TEST_MINI_CODE
} from '../constants/constants';
import * as Crypto from "expo-crypto";
import Purchases from "react-native-purchases";
import DeviceInfo from "react-native-device-info";
import {checkVersion} from "../util/VersionApi";
import LoadingOverlay from "../ui/LoadingOverlay";

function CategoryScreen({navigation}) {
    const [categories, setCategories] = useState([]);
    const [isLoading, setIsLoading] = useState(false);

    const authCtx = useContext(AuthContext);

    async function checkNewVersion() {
        try {
            const responseData = await checkVersion(DeviceInfo.getVersion(), DeviceInfo.getBuildNumber(),
                Platform.OS);
            console.log(responseData);
            if (responseData.haveNewVersion) {
                if (responseData.forceDownload) {
                    Alert.alert(
                        "Notification",
                        "There is a new update, please download it to continue using",
                        [{
                            text: "Download",
                            onPress: () => goStore(responseData.iosStoreUrl, responseData.androidStoreUrl)
                        }],
                        {cancelable: false}
                    );
                } else {
                    Alert.alert(
                        "Notification",
                        "There is a new update, do you want to download it?",
                        [{
                            text: "Cancel",
                            style: 'cancel'
                        }, {
                            text: "Download",
                            style: 'destructive',
                            onPress: () => goStore(responseData.iosStoreUrl, responseData.androidStoreUrl)
                        }],
                        {cancelable: true}
                    )
                }
            }
        } catch (e) {
            console.log(e);
        }
    }

    async function goStore(iosStoreUrl, androidStoreUrl) {
        if (Platform.OS === 'ios') {
            await Linking.openURL(iosStoreUrl);
        } else {
            await Linking.openURL(androidStoreUrl);
        }
    }

    async function getCategoryData() {
        try {
            setIsLoading(true);
            const categoryData = await getQuizCategoryList(authCtx);
            setCategories(categoryData);
        } finally {
            setIsLoading(false);
        }
    }

    async function loginRevenueCat() {
        if (!ENABLE_REVENUE_CAT) {
            return;
        }
        const digest = await Crypto.digestStringAsync(
            Crypto.CryptoDigestAlgorithm.MD5,
            authCtx.email
        );
        //console.log("Login RevenueCat with id", digest);
        await Purchases.logIn(digest);
    }

    useEffect(() => {
        checkNewVersion();
    }, [])

    useEffect(() => {
        loginRevenueCat();
    }, [authCtx])

    useEffect(() => {
        getCategoryData();
    }, [])

    function renderCategoryItem(itemData) {
        function pressHandler() {
            const numberOfQuiz = {};
            numberOfQuiz[TYPE_TEST_MINI_CODE] = itemData.item.numMiniTest;
            numberOfQuiz[TYPE_TEST_FULL_CODE] = itemData.item.numFullTest;

            navigation.navigate("CategoryDetail", {
                categoryCode: itemData.item.code,
                categoryTitle: itemData.item.title,
                categoryHeader: itemData.item.header,
                categoryOffer: itemData.item.offer,
                categoryEntitlement: itemData.item.entitlement,
                numberOfQuiz: numberOfQuiz,
                purchasedInfoApi: itemData.item.purchasedInfo
            });
        }

        let style = {marginLeft: 12};
        if (itemData.index % 2 === 0) {
            style = {marginLeft: 0};
        }

        return <CategoryGridTile
            imageUri={itemData.item.imageUri}
            numFullTest={itemData.item.numFullTest}
            numMiniTest={itemData.item.numMiniTest}
            style={style}
            onPress={pressHandler}/>;
    }

    if (isLoading) {
        return <LoadingOverlay message={'Loading...'}/>;
    }

    return (
        <Background>
            <FlatList style={{paddingHorizontal: 14, paddingTop: 20}}
                      data={categories}
                      keyExtractor={(item) => item.code}
                      renderItem={renderCategoryItem}
                      refreshControl={<RefreshControl refreshing={isLoading} onRefresh={getCategoryData}/>}
                      numColumns={2}/>

            {SHOW_BANNER_AD && <BannerAd
                unitId={BANNER_AD_UNIT_ID}
                size={BannerAdSize.ANCHORED_ADAPTIVE_BANNER}
                requestOptions={{
                    requestNonPersonalizedAdsOnly: true,
                }}
            />}
        </Background>
    );
}

export default CategoryScreen;
