import {Platform, StyleSheet, Text, View} from "react-native";

function TextNote({children, color, style}) {
  return (
    <>
      <View style={[styles.noteStyle, style, {backgroundColor: color ? color : null}]} />
      <Text style={styles.textStyle}>{children}</Text>
    </>
  );
}

export default TextNote;

const styles = StyleSheet.create({
  noteStyle: {
    width: 15,
    height: 15,
    marginLeft: 10,
    marginRight: 5
  },
  textStyle: {
    fontSize: Platform.select({ios: 14, android: 13})
  }
});
