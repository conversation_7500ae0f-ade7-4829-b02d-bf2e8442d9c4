import {FlatList, StyleSheet, Text, View} from "react-native";
import ArticleCategoryGridTile from "./ArticleCategoryGridTile";
import {useNavigation} from "@react-navigation/native";

function ArticleSpaceGridTile({spaceName, categories}) {
    const navigation = useNavigation();

    function renderCategoryItem(itemData) {
        function pressHandler() {
            navigation.navigate("Articles", {
                categoryId: itemData.item.id,
                categoryName: itemData.item.name
            });
        }

        let style = {marginLeft: 12};
        if (itemData.index % 2 === 0) {
            style = {marginLeft: 0};
        }

        return <ArticleCategoryGridTile
            categoryName={itemData.item.name}
            style={style}
            onPress={pressHandler}/>;
    }

    return (
        <View>
            <Text style={styles.spaceName}>{spaceName}</Text>
            <FlatList data={categories}
                      keyExtractor={(item) => item.id}
                      listKey={(item) => item.id}
                      renderItem={renderCategoryItem}
                      numColumns={2}/>
        </View>
    );
}

export default ArticleSpaceGridTile;

const styles = StyleSheet.create({
    spaceName: {
        fontWeight: 'bold',
        fontSize: 16,
        marginBottom: 5
    }
});
