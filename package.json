{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "scripts": {"start": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@native-html/table-plugin": "^5.3.1", "@react-native-async-storage/async-storage": "1.18.2", "@react-native-clipboard/clipboard": "^1.11.2", "@react-native-community/cli-platform-ios": "^10.1.1", "@react-native-community/datetimepicker": "7.2.0", "@react-native-community/slider": "4.4.2", "@react-native-firebase/analytics": "^18.1.0", "@react-native-firebase/app": "^18.1.0", "@react-native-firebase/crashlytics": "^18.1.0", "@react-navigation/drawer": "^6.5.0", "@react-navigation/elements": "^1.3.17", "@react-navigation/material-top-tabs": "^6.6.2", "@react-navigation/native": "^6.0.13", "@react-navigation/native-stack": "^6.9.1", "@react-navigation/stack": "^6.3.16", "axios": "^1.5.1", "expo": "^49.0.23", "expo-build-properties": "~0.8.3", "expo-checkbox": "~2.4.0", "expo-constants": "~14.4.2", "expo-crypto": "~12.4.1", "expo-dev-client": "~2.4.13", "expo-secure-store": "~12.3.1", "expo-splash-screen": "~0.20.5", "expo-status-bar": "~1.6.0", "expo-system-ui": "~2.4.0", "expo-tracking-transparency": "~3.1.0", "expo-updates": "~0.18.19", "jwt-decode": "^3.1.2", "moment": "^2.29.4", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.72.10", "react-native-app-intro-slider": "^4.0.4", "react-native-countdown-fixed": "^2.7.1", "react-native-device-info": "^10.4.0", "react-native-dialog": "^9.3.0", "react-native-element-dropdown": "^2.9.0", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "~2.12.0", "react-native-google-mobile-ads": "^12.3.0", "react-native-image-viewing": "^0.2.2", "react-native-keychain": "^8.1.1", "react-native-modal-datetime-picker": "^14.0.0", "react-native-pager-view": "6.2.0", "react-native-progress": "^5.0.0", "react-native-purchases": "^8.0.1", "react-native-radio-buttons-group": "^2.2.11", "react-native-reanimated": "~3.3.0", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-sound-player": "^0.13.2", "react-native-svg": "13.9.0", "react-native-tab-view": "^3.5.1", "react-native-toast-message": "^2.1.6", "react-native-video": "^5.2.1", "react-native-web": "~0.19.6", "react-native-webview": "13.2.2", "react-native-youtube-iframe": "^2.3.0", "validator": "^13.9.0"}, "devDependencies": {"@babel/core": "^7.20.0", "babel-plugin-transform-inline-environment-variables": "^0.4.4", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0"}, "private": true}