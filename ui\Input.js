import {Platform, Pressable, StyleSheet, Text, TextInput, View} from 'react-native';
import {Colors} from '../constants/styles';
import {Feather} from '@expo/vector-icons';
import {useState} from 'react';

function Input({
                 label,
                 keyboardType,
                 secure,
                 onUpdateValue,
                 isDisable,
                 rightIcon,
                 rightIconColor,
                 placeholder,
                 isInvalid,
                 value
               }) {
  const [enteredValue, setEnteredValue] = useState(value);
  const [isSecure, setIsSecure] = useState(secure);
  const [rightIconName, setRightIconName] = useState(rightIcon);

  let inputBackgroundStyle = {};
  if (isDisable) {
    inputBackgroundStyle = {
      backgroundColor: Colors.accent300,
    }
  } else if (isInvalid) {
    inputBackgroundStyle = {
      backgroundColor: Colors.error100,
    }
  }

  function onChangeTextHandler(enteredValue) {
    setEnteredValue(enteredValue);
    onUpdateValue(enteredValue);
  }

  function onRightIconPressHandler() {
    switch (rightIconName) {
      case 'x-circle':
        onChangeTextHandler('');
        break;
      case 'eye':
      case 'eye-off':
        const newSecure = !isSecure;
        setIsSecure(newSecure);
        setRightIconName(newSecure ? 'eye' : 'eye-off')
        break;
    }
  }

  return (
    <View style={styles.container}>
      <Text style={[styles.label, isInvalid && styles.labelInvalid]}>
        {label}
      </Text>
      <View style={[styles.inputContainer, inputBackgroundStyle]}>
        <TextInput
          style={styles.input}
          autoCorrect={false}
          autoCapitalize="none"
          keyboardType={keyboardType}
          secureTextEntry={isSecure}
          onChangeText={onChangeTextHandler}
          editable={!isDisable}
          placeholder={placeholder}
          textContentType='oneTimeCode'
          value={enteredValue}
        />
        {rightIconName && <Pressable onPress={onRightIconPressHandler}>
          <Feather name={rightIconName} size={24} color={rightIconColor} />
        </Pressable>}
      </View>
    </View>
  );
}

export default Input;

const styles = StyleSheet.create({
  container: {
    marginTop: 25
  },
  label: {
    color: Colors.accent100,
    marginBottom: 4,
    fontSize: 14,
    fontWeight: '500'
  },
  labelInvalid: {
    color: Colors.error500,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    borderRadius: 4,
    elevation: 4,
    backgroundColor: 'white',
    shadowColor: 'black',
    shadowOpacity: 0.25,
    shadowOffset: {width: 0, height: 2},
    shadowRadius: 8,
    overflow: Platform.select({ios: 'visible', android: 'hidden'})
  },
  input: {
    flex: 1,
    height: 55,
    fontSize: 16,
    color: Colors.accent100,
  }
});
