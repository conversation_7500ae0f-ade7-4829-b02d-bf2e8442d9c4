import {StyleSheet, Text, useWindowDimensions, View} from 'react-native';
import IconRoundButton from '../ui/IconRoundButton';
import {Colors} from '../constants/styles';
import AnswerItem from './AnswerItem';
import RenderHtml from 'react-native-render-html';
import {newHtmlProps} from "../util/RenderHtmlRenfers";

function TestDetailBody({
                            currQuestionIdx,
                            totalQuestion,
                            questionData,
                            reviewOnPress,
                            updateAnswerHandler,
                            isFinish,
                            isDisable
                        }) {

    const width = useWindowDimensions().width;

    const tagsStyles = {
        body: {
            fontSize: 16,
            lineHeight: 25,
            textAlign: 'justify'
        },
        img: {
            maxWidth: width * 0.9,
            height: 'auto'
        },
        p: {
            marginVertical: 5
        },
        ol: {
            marginVertical: 5
        },
        iframe: {
            opacity: 0.99
        },
        // If you are using @native-html/table-plugin
        table: {
            opacity: 0.99
        }
    };

    function renderAnswerItem(itemData, type) {
        const index = itemData.item.index;
        return <AnswerItem key={index}
                           index={index}
                           isFinish={isFinish}
                           isDisable={isDisable}
                           selected={itemData.item.selected}
                           correct={itemData.item.correct}
                           answeredCorrect={itemData.item.answeredCorrect}
                           type={type}
                           answerText={itemData.item.answer}
                           onPress={updateAnswerHandler}/>
    }

    return (
        <>
            <View style={styles.questionIndexContainer}>
                <View style={{flex: 1}}>
                    <Text style={styles.questionIndexText}>Question
                        <Text style={{fontWeight: 'bold'}}> {currQuestionIdx + 1} </Text>
                        of
                        <Text style={{fontWeight: 'bold'}}> {totalQuestion}</Text>
                    </Text>
                </View>

                {reviewOnPress && <View style={{flex: 1, alignItems: 'flex-end'}}>
                    <IconRoundButton icon='bookmark-check-outline'
                                     color={Colors.primary100}
                                     onPress={reviewOnPress}
                                     noBackground={true}
                                     width={25}
                                     height={25}
                                     margin={5}
                                     size={30}/>
                </View>}
            </View>

            {/*<Text style={styles.questionText}>{questionData.question}</Text>*/}

            <RenderHtml
                contentWidth={width}
                tagsStyles={tagsStyles}
                source={{html: questionData.question}}
                {...newHtmlProps}
            />

            {questionData.answerType !== 'single' &&
                <View style={{flexGrow: 0, minHeight: 70}}>
                    {
                        questionData.answerData.map((item) => {
                            return renderAnswerItem({'item': item}, 'multi');
                        })
                    }
                </View>
            }

            {questionData.answerType === 'single' &&
                <View style={{flexGrow: 0, minHeight: 70}}>
                    {
                        questionData.answerData.map((item) => {
                            return renderAnswerItem({'item': item}, 'single');
                        })
                    }
                </View>
            }
        </>
    );
}

export default TestDetailBody;

const styles = StyleSheet.create({
    questionIndexContainer: {
        marginTop: 0,
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
    },
    questionIndexText: {
        fontSize: 16,
        marginBottom: 8
    },
    questionText: {
        fontSize: 15,
        lineHeight: 25
    }
});
