# BÁO CÁO PHÂN TÍCH DỰ ÁN EZAMI

## Tổng quan dự án
**Ezami** là một ứng dụng giáo dục di động được xây dựng bằng React Native và Expo, cung cấp hệ thống thi trắc nghiệm tương tác và nội dung giáo dục. Ứng dụng hỗ trợ đa nền tảng (iOS/Android) với tích hợp thanh toán, quảng cáo và analytics.

---

# 1. <PERSON>h sách tính năng

- **Feature 1: Hệ thống Authentication & User Management** - <PERSON><PERSON><PERSON>h<PERSON><PERSON>, đ<PERSON><PERSON> ký, quên mật khẩu, quản lý tài khoản người dùng với JWT token
- **Feature 2: Hệ thống Quiz/Test Interactive** - Làm bài thi trắc nghiệm với nhiều loại câu hỏi, theo dõi tiế<PERSON> đ<PERSON>, lư<PERSON> và tiếp tục bài thi
- **Feature 3: Quản lý Category & Test Types** - Phân loại bài thi theo chủ đề và loại (Mini/Full test)
- **Feature 4: Hệ thống Article/Content Management** - Đọc bài viết giáo dục, bookmark, theo dõi tiến độ đọc
- **Feature 5: Payment & Subscription System** - Thanh toán in-app, quản lý subscription qua RevenueCat
- **Feature 6: Advertising Integration** - Hiển thị quảng cáo Google AdMob với điều kiện premium
- **Feature 7: Offline Data Management** - Lưu trữ dữ liệu offline, đồng bộ khi online
- **Feature 8: Analytics & Tracking** - Firebase Analytics, Crashlytics để theo dõi hành vi người dùng
- **Feature 9: Onboarding & Settings** - Hướng dẫn người dùng mới, cài đặt ứng dụng
- **Feature 10: Version Management** - Kiểm tra và cập nhật phiên bản ứng dụng

---

# 2. Chi tiết tính năng và file liên quan

## Feature 1: Hệ thống Authentication & User Management
- **screens/LoginScreen.js**: Giao diện đăng nhập
- **screens/RegisterScreen.js**: Giao diện đăng ký tài khoản
- **screens/ForgetPasswordScreen.js**: Giao diện quên mật khẩu
- **screens/AccountSettingScreen.js**: Quản lý thông tin tài khoản
- **screens/ChangePasswordScreen.js**: Đổi mật khẩu
- **store/AuthContext.js**: Quản lý state authentication, JWT token
- **util/AuthApi.js**: API calls cho authentication
- **util/UserApi.js**: API calls cho user management
- **components/LoginForm.js**: Form component cho đăng nhập/đăng ký

## Feature 2: Hệ thống Quiz/Test Interactive
- **screens/TestDetailScreen.js**: Màn hình làm bài thi chính
- **screens/TestsScreen.js**: Danh sách bài thi theo category
- **components/TestDetailHeader.js**: Header hiển thị thông tin bài thi
- **components/TestDetailBody.js**: Nội dung câu hỏi và đáp án
- **components/TestDetailFooter.js**: Footer điều hướng câu hỏi
- **components/AnswerItem.js**: Component hiển thị từng đáp án
- **components/ResultForm.js**: Hiển thị kết quả bài thi
- **store/QuizContext.js**: Quản lý state quiz, lưu tiến độ
- **util/QuizApi.js**: API calls cho quiz/test
- **models/Test.js**: Model dữ liệu bài thi
- **models/Question.js**: Model dữ liệu câu hỏi
- **models/Answer.js**: Model dữ liệu đáp án

## Feature 3: Quản lý Category & Test Types
- **screens/CategoryScreen.js**: Danh sách categories chính
- **screens/CategoryDetailScreen.js**: Chi tiết category và test types
- **components/CategoryGridTile.js**: Component hiển thị category
- **components/CategoryDetailGridTile.js**: Component hiển thị test types
- **components/TestGridTile.js**: Component hiển thị bài thi
- **models/Category.js**: Model dữ liệu category
- **models/TestType.js**: Model dữ liệu test type
- **constants/constants.js**: Định nghĩa constants cho test types

## Feature 4: Hệ thống Article/Content Management
- **screens/ArticleCategoryScreen.js**: Danh sách categories bài viết
- **screens/ArticlesScreen.js**: Danh sách bài viết theo category
- **screens/ArticleDetailScreen.js**: Chi tiết bài viết
- **components/ArticleListComponent.js**: Component hiển thị bài viết
- **components/ArticleGridTile.js**: Component grid bài viết
- **util/PostApi.js**: API calls cho articles
- **models/Article.js**: Model dữ liệu bài viết
- **models/ArticleCategory.js**: Model category bài viết
- **models/ArticleSpace.js**: Model space bài viết

## Feature 5: Payment & Subscription System
- **screens/PaywallScreen.js**: Màn hình thanh toán subscription
- **components/PackageItem.js**: Component hiển thị gói subscription
- **constants/constants.js**: Cấu hình RevenueCat API keys
- **App.js**: Khởi tạo RevenueCat

## Feature 6: Advertising Integration
- **screens/CategoryScreen.js**: Hiển thị banner ads
- **screens/TestsScreen.js**: Hiển thị banner ads
- **screens/ArticlesScreen.js**: Hiển thị banner ads
- **constants/constants.js**: Cấu hình Ad Unit IDs
- **App.js**: Khởi tạo Google Mobile Ads

## Feature 7: Offline Data Management
- **store/AuthContext.js**: Lưu trữ token offline
- **store/QuizContext.js**: Lưu trữ tiến độ quiz offline
- **screens/ArticlesScreen.js**: Lưu trữ trạng thái đọc bài viết
- **util/Utils.js**: Utility functions cho data management

## Feature 8: Analytics & Tracking
- **App.js**: Khởi tạo Firebase Analytics và Crashlytics
- **screens/**: Tracking screen views trong navigation

## Feature 9: Onboarding & Settings
- **screens/OnboardingScreen.js**: Hướng dẫn người dùng mới
- **screens/SettingScreen.js**: Cài đặt ứng dụng
- **screens/ContactScreen.js**: Thông tin liên hệ
- **screens/SoftwareInfoScreen.js**: Thông tin phần mềm
- **store/AppContext.js**: Quản lý state onboarding

## Feature 10: Version Management
- **util/VersionApi.js**: API kiểm tra phiên bản
- **models/CheckVersion.js**: Model kiểm tra version
- **screens/CategoryScreen.js**: Logic kiểm tra version mới

---

# 3. Flow code theo function

## Authentication System

### File: store/AuthContext.js
#### Function: authentication
- **Chức năng**: Xác thực và lưu trữ JWT token
- **Flow xử lý**:
  1. Tạo object auth chứa token và email
  2. Cập nhật state với token mới
  3. Lưu token vào AsyncStorage để persist offline

```javascript
async function authentication(token, email) {
  try {
    // Step 1: Tạo object auth chứa token và email
    const auth = {
      'token': token,
      'email': email
    };
    // Step 2: Cập nhật state với token mới
    setAuthToken(auth);
    // Step 3: Lưu token vào AsyncStorage để persist offline
    await AsyncStorage.setItem('token', JSON.stringify(auth));
  } catch (ex) {
    console.log(ex)
  }
}
```

#### Function: isAuthenticated
- **Chức năng**: Kiểm tra tính hợp lệ của JWT token
- **Flow xử lý**:
  1. Kiểm tra token và email có tồn tại
  2. Decode JWT token để lấy thông tin expiration
  3. Kiểm tra token có hết hạn chưa

```javascript
function isAuthenticated() {
  try {
    // Step 1: Kiểm tra token và email có tồn tại
    if (authToken && authToken.token && authToken.email) {
      // Step 2: Decode JWT token để lấy thông tin expiration
      let decodedToken = jwt_decode(authToken.token);
      let currentDate = new Date();

      // Step 3: Kiểm tra token có hết hạn chưa (JWT exp tính bằng seconds)
      if (decodedToken.exp * 1000 < currentDate.getTime() - 86400 * 1000) {
        console.warn("Token expired.");
        return false;
      } else {
        console.info("Valid token");
        return true;
      }
    } else {
      return false;
    }
  } catch (ex) {
    console.log(ex);
  }
  return false;
}
```

## Quiz System

### File: util/QuizApi.js
#### Function: getQuestions
- **Chức năng**: Lấy danh sách câu hỏi cho một bài thi
- **Flow xử lý**:
  1. Chuẩn bị headers với Bearer token
  2. Gọi API lấy questions của quiz
  3. Parse response data thành Question objects
  4. Map các properties từ API response
  5. Parse answer data cho từng câu hỏi
  6. Tạo Test object với questions đã parse
  7. Xử lý exception, logout nếu 401 Unauthorized

```javascript
export async function getQuestions(authCtx, quizId) {
  try {
    // Step 1: Chuẩn bị headers với Bearer token
    const headers = {Authorization: `Bearer ${authCtx.token}`};
    
    // Step 2: Gọi API lấy questions của quiz
    const response = await axios.get(BE_URL + "/api/quiz/" + quizId + "/question", {headers});

    const data = [];
    // Step 3: Parse response data thành Question objects
    for (const item of response.data.data) {
      const question = new Question();
      // Step 4: Map các properties từ API response
      question.id = item.id;
      question.quizId = item.quizId;
      question.sort = item.sort;
      
      // Step 5: Parse answer data cho từng câu hỏi
      const answerData = [];
      for (const answer of item.answerData) {
        answerData.push(new Answer(answer.id, answer.answer, answer.correct, answer.points));
      }
      question.answerData = answerData;
      data.push(question);
    }

    // Step 6: Tạo Test object với questions đã parse
    const test = new Test(response.data.data.id, response.data.data.slug, null, null, 
      response.data.data.name, response.data.data.timeLimit, data.length,
      null, response.data.data.postContent, response.data.data.showExplain);
    test.questions = data;

    return test;
  } catch (e) {
    // Step 7: Xử lý exception, logout nếu 401 Unauthorized
    handleException(e, authCtx);
  }
  return null;
}
```

#### Function: submitAnswerData
- **Chức năng**: Gửi đáp án và nhận kết quả bài thi
- **Flow xử lý**:
  1. Chuẩn bị URL và headers
  2. Convert Map answerData thành array format cho API
  3. Gửi POST request với thời gian và đáp án
  4. Parse response thành ResultResponse object

```javascript
export async function submitAnswerData(token, quizId, answerData, startTime, endTime, elapsedTime) {
  // Step 1: Chuẩn bị URL và headers
  const url = BE_URL + "/api/quiz/" + quizId;
  const headers = {Authorization: `Bearer ${token}`};

  // Step 2: Convert Map answerData thành array format cho API
  const data = [];
  answerData.forEach(function (value, key) {
    data.push({
      "questionId": key,
      "answerData": value
    })
  })

  // Step 3: Gửi POST request với thời gian và đáp án
  const response = await axios.post(url, {
    "startTime": startTime,
    "endTime": endTime,
    "elapsedTime": elapsedTime,
    "data": data
  }, {headers});

  // Step 4: Parse response thành ResultResponse object
  const item = response.data.data;
  return new ResultResponse(item.corrects, item.totalQuestion, item.answeredTime, 
    item.point, item.totalPoint, item.percentage, item.passPercentage, 
    item.pass, item.resultText);
}
```

### File: screens/TestDetailScreen.js
#### Function: submitAnswer
- **Chức năng**: Xử lý submit bài thi và hiển thị kết quả
- **Flow xử lý**:
  1. Disable autoplay audio/video
  2. Tính toán thời gian làm bài
  3. Gọi API submitAnswerData để gửi đáp án
  4. Cập nhật state với kết quả
  5. Chuyển về câu hỏi đầu tiên để review
  6. Gọi success handler

```javascript
async function submitAnswer() {
  setIsLoading(true);
  try {
    // Step 1: Disable autoplay audio/video
    quizCtx.disableAutoPlay();
    
    // Step 2: Tính toán thời gian làm bài
    const elapsedTime = testTime.totalElapsedTime + testTime.groupElapsedTime;
    
    // Step 3: Gọi API submitAnswerData để gửi đáp án
    const resultData = await submitAnswerData(authCtx.token, testId, answeredMap, startTime,
      Math.floor(Date.now() / 1000), elapsedTime);
    
    // Step 4: Cập nhật state với kết quả
    setResultResponse(resultData);
    setTestStatus(TestStatus.FINISHED);
    
    // Step 5: Chuyển về câu hỏi đầu tiên để review
    nextQuestion(0);
    
    // Step 6: Gọi success handler
    submitSuccessHandler();
  } catch (e) {
    console.log(e);
    Alert.alert("Error", "An error occurred, please try again later");
  } finally {
    setIsLoading(false);
  }
}
```

## Offline Data Management

### File: store/QuizContext.js
#### Function: saveQuiz
- **Chức năng**: Lưu tiến độ làm bài offline
- **Flow xử lý**:
  1. Convert Map objects thành JSON serializable format
  2. Lưu vào AsyncStorage với key unique cho từng quiz

```javascript
async function saveQuiz(quizId, quizProgressData) {
  try {
    // Step 1: Convert Map objects thành JSON serializable format
    // Step 2: Lưu vào AsyncStorage với key unique cho từng quiz
    await AsyncStorage.setItem('quiz_data_' + quizId, JSON.stringify(quizProgressData, replacer));
  } catch (ex) {
    console.log(ex)
  }
}

// Helper function để serialize Map objects
function replacer(key, value) {
  if(value instanceof Map) {
    return {
      dataType: 'Map',
      value: Array.from(value.entries()),
    };
  } else {
    return value;
  }
}
```

---

## Kết luận

Dự án Ezami được xây dựng với kiến trúc rõ ràng, phân tách concerns tốt và có hệ thống offline/online sync hiệu quả. Ứng dụng tích hợp đầy đủ các tính năng cần thiết cho một platform giáo dục hiện đại bao gồm authentication, quiz system, content management, payment integration và analytics tracking.

**Điểm mạnh:**
- Kiến trúc modular với separation of concerns rõ ràng
- Hỗ trợ offline-first với sync capabilities
- Tích hợp payment và monetization hoàn chỉnh
- Analytics và tracking chi tiết
- Cross-platform support

**Khuyến nghị cải thiện:**
- Thêm unit tests cho các functions quan trọng
- Implement error boundary cho better error handling
- Optimize performance với lazy loading
- Thêm accessibility features
