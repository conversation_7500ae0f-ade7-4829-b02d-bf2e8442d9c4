import axios from 'axios';
import Test from '../models/Test';
import {BE_URL} from '../constants/constants';
import Answer from '../models/Answer';
import Question from '../models/Question';
import ResultResponse from '../models/ResultResponse';
import Category from '../models/Category';

export async function getQuizList(authCtx, category, typeTest) {
  try {
    const headers = {Authorization: `Bearer ${authCtx.token}`};

    let url = BE_URL + "/api/quiz?category=" + (category ? category : '') + "&typeTest=" +
              (typeTest ? typeTest : '');
    const response = await axios.get(url, {headers});

    const data = [];
    for (const item of response.data.data) {
      data.push(new Test(item.id, item.slug, null, null, item.name, item.timeLimit / 60, item.questions,
        item.answeredQuestions === null ? 0 : item.answeredQuestions,
        item.postContent, null, item.answeredPoints, item.totalPoints, item.pass));
    }
    return data;
  } catch (e) {
    handleException(e, authCtx);
  }
  return [];
}

export async function getQuizCategoryList(authCtx) {
  try {
    const headers = {Authorization: `Bearer ${authCtx.token}`};
    const response = await axios.get(BE_URL + "/api/quiz/category", {headers});

    const data = [];
    for (const item of response.data.data) {
      data.push(
        new Category(item.code, item.header, item.title, item.imageUri, item.numFullTest, item.numMiniTest,
          item.offer,
          item.entitlement, item.purchasedInfo?.isPurchased, item.purchasedInfo?.fromTime,
          item.purchasedInfo?.toTime));
    }
    return data;
  } catch (e) {
    handleException(e, authCtx);
  }
  return [];
}

export async function getQuestions(authCtx, quizId) {
  try {
    const headers = {Authorization: `Bearer ${authCtx.token}`};
    const response = await axios.get(BE_URL + "/api/quiz/" + quizId, {headers});

    const data = [];
    for (const item of response.data.data.questionList) {
      const answerData = [];

      for (const answerItem of item.answerData) {
        answerData.push(new Answer(answerItem.answer, answerItem.correct, answerItem.point, answerItem.index));
      }

      const question = new Question();
      question.id = item.id;
      question.quizId = item.quizId;
      question.sort = item.sort;
      question.title = item.title;
      question.points = item.points;
      question.question = item.question;
      question.correctMsg = item.correctMsg;
      question.incorrectMsg = item.incorrectMsg;
      question.correctSameText = item.correctSameText;
      question.tipEnabled = item.tipEnabled;
      question.tipMsg = item.tipMsg;
      question.answerType = item.answerType;
      question.showPointsInBox = item.showPointsInBox;
      question.answerPointsActivated = item.answerPointsActivated;
      question.categoryId = item.categoryId;
      question.answerPointsDiffModusActivated = item.answerPointsDiffModusActivated;
      question.disableCorrect = item.disableCorrect;
      question.matrixSortAnswerCriteriaWidth = item.matrixSortAnswerCriteriaWidth;
      question.answerData = answerData;

      data.push(question);
    }

    const test = new Test(response.data.data.id, response.data.data.slug, null, null, response.data.data.name,
      response.data.data.timeLimit, data.length,
      null, response.data.data.postContent, response.data.data.showExplain);
    test.questions = data;

    return test;
  } catch (e) {
    handleException(e, authCtx);
  }
  return null;
}

function handleException(e, authCtx) {
  console.error(e);
  if (e.response.status === 401) {
    authCtx.logout();
  }
}

export async function submitAnswerData(token, quizId, answerData, startTime, endTime, elapsedTime) {
  const url = BE_URL + "/api/quiz/" + quizId;
  const headers = {Authorization: `Bearer ${token}`};

  const data = [];
  answerData.forEach(function (value, key) {
    data.push({
      "questionId": key,
      "answerData": value
    })
  })
  console.log(startTime);
  console.log(endTime);

  const response = await axios.post(url, {
    "startTime": startTime,
    "endTime": endTime,
    "elapsedTime": elapsedTime,
    "data": data
  }, {headers});

  const responseData = response.data.data;
  return new ResultResponse(responseData.point, responseData.totalPoint, responseData.percentage,
    responseData.passPercentage, responseData.pass, responseData.corrects, responseData.questions,
    responseData.answeredTime, responseData.resultText);
}
