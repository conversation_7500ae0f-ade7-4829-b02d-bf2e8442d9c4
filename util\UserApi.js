import axios from 'axios';
import {BE_URL} from '../constants/constants';

export async function signup(email, password) {
  const url = BE_URL + "/signup";
  const response = await axios.post(url, {
    "email": email.trim(),
    "password": password.trim()
  });
  return response.data;
}

export async function register(email, password, code) {
  const url = BE_URL + "/register";
  const response = await axios.post(url, {
    "email": email.trim(),
    "password": password.trim(),
    "verificationCode": code.trim()
  });
  return response.data;
}

export async function resetPass(email, password, code) {
  const url = BE_URL + "/api/user/reset-pass";
  const response = await axios.post(url, {
    "email": email.trim(),
    "password": password.trim(),
    "verificationCode": code.trim()
  });
  return response.data;
}

export async function getUserInfo(token) {
  const headers = {Authorization: `Bearer ${token}`};
  const response = await axios.get(BE_URL + "/api/user/info", {headers});

  return response.data.data;
}

export async function updateUserInfo(token, newEmail, mobile, fullName, dob) {
  const headers = {Authorization: `Bearer ${token}`};
  const url = BE_URL + "/api/user/update";
  const response = await axios.post(url, {
    "email": newEmail.trim(),
    "mobile": mobile ? mobile.trim() : "",
    "fullName": fullName.trim(),
    "dob": dob ? dob.trim() : ""
  }, {headers});
  return response.data;
}

export async function updatePass(token, oldPass, newPass) {
  const headers = {Authorization: `Bearer ${token}`};
  const url = BE_URL + "/api/user/change-pass";

  const response = await axios.post(url, {
    "oldPass": oldPass.trim(),
    "newPass": newPass.trim()
  }, {headers});
  return response.data;
}

export async function deleteAccount(token) {
  const headers = {Authorization: `Bearer ${token}`};
  const url = BE_URL + "/api/user/delete";
  const response = await axios.post(url, {}, {headers});
  return response.data;
}
