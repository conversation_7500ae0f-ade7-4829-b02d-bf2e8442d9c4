import LoginForm from '../components/LoginForm';
import {useState} from 'react';
import LoadingOverlay from '../ui/LoadingOverlay';
import Background from '../ui/Background';
import {register} from '../util/UserApi';
import {Alert} from 'react-native';
import {useNavigation} from '@react-navigation/native';

function RegisterScreen() {
    const navigation = useNavigation();
    const [isAuthenticating, setIsAuthenticating] = useState(false);

    async function registerHandler({email, password, confirmationCode}) {
        setIsAuthenticating(true);

        try {
            const response = await register(email, password, confirmationCode);
            console.log(response);
            if (response.code === 200) {
                Alert.alert(
                    "Success",
                    "Your account has been successfully registered",
                    [{
                        text: "OK",
                        onPress: () => navigation.replace('Login')
                    }]
                );
            } else {
                Alert.alert("Register failed", response.message);
            }
        } catch (e) {
            Alert.alert('Register failed',
                'An error occurred, please try again later');
            console.log(e);
        } finally {
            setIsAuthenticating(false);
        }
    }

    if (isAuthenticating) {
        return <LoadingOverlay message={'Registering...'}/>;
    }

    return (
        <Background>
            <LoginForm onSubmit={registerHandler} isLogin={false}/>
        </Background>
    );
}

export default RegisterScreen;
