import LoginForm from '../components/LoginForm';
import Background from '../ui/Background';
import {useContext, useState} from 'react';
import LoadingOverlay from '../ui/LoadingOverlay';
import {AuthContext} from '../store/AuthContext';
import {Alert} from 'react-native';
import {login} from '../util/AuthApi';

function LoginScreen() {
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const authCtx = useContext(AuthContext);

  async function loginHandler({email, password}) {
    setIsAuthenticating(true);

    try {
      const token = await login(email, password);
      authCtx.authentication(token, email);
    } catch (e) {
      Alert.alert('Authentication failed',
        'Could not log you in. Please check your email and password and try again, or contact customer support for assistance');
      authCtx.logout();
    } finally {
      setIsAuthenticating(false);
    }
  }

  if (isAuthenticating) {
    return <LoadingOverlay message={'Logging you in...'} />;
  }

  return (
    <Background>
      <LoginForm onSubmit={loginHandler} isLogin={true} />
    </Background>
  );
}

export default LoginScreen;
