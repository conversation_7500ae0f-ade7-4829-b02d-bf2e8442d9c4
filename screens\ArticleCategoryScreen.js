import {<PERSON><PERSON><PERSON>, RefreshControl, View, StyleSheet, Text} from "react-native";
import {BANNER_ARTICLE_AD_UNIT_ID, SHOW_BANNER_AD} from "../constants/constants";
import {BannerAd, BannerAdSize} from "react-native-google-mobile-ads";
import Background from "../ui/Background";
import ArticleSpaceGridTile from "../components/ArticleSpaceGridTile";
import {useContext, useEffect, useState} from "react";
import {getArticleSpaces} from "../util/PostApi";
import {AuthContext} from "../store/AuthContext";
import LoadingOverlay from "../ui/LoadingOverlay";
import {SelectCountry} from 'react-native-element-dropdown';

function ArticleCategoryScreen() {
    const [spaces, setSpaces] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [language, setLanguage] = useState("vn");

    const authCtx = useContext(AuthContext);

    const COUNTRY_DATA = [
        {
            value: 'vn',
            label: 'Tiếng Việt',
            image: require('../assets/flags/vn.png'),
        },
        {
            value: 'en',
            label: 'English',
            image: require('../assets/flags/us.png'),
        }
    ];

    useEffect(() => {
        getSpaceData(language);
    }, [])

    async function getSpaceData(language) {
        try {
            setIsLoading(true);
            const spaceData = await getArticleSpaces(authCtx, language);
            setSpaces(spaceData);
        } catch (ex) {
            console.log(ex);
        } finally {
            setIsLoading(false);
        }
    }

    async function onchangeLanguage(language) {
        setLanguage(language);
        await getSpaceData(language);
    }

    function renderSpace(itemData) {
        return <ArticleSpaceGridTile
            spaceName={itemData.item.name}
            categories={itemData.item.categories}/>;
    }

    if (isLoading) {
        return <LoadingOverlay message={'Loading...'}/>;
    }

    return (
        <Background>
            <View style={{justifyContent: 'flex-end', flexDirection: 'row', alignItems: 'center'}}>
                <Text style={styles.languageText}>Language</Text>
                <SelectCountry
                    style={styles.dropdown}
                    selectedTextStyle={styles.selectedTextStyle}
                    imageStyle={styles.imageStyle}
                    maxHeight={200}
                    value={language}
                    data={COUNTRY_DATA}
                    valueField="value"
                    labelField="label"
                    imageField="image"
                    placeholder="Select language"
                    onChange={e => onchangeLanguage(e.value)}
                />
            </View>

            <FlatList
                contentContainerStyle={{paddingHorizontal: 14, paddingTop: 14, paddingBottom: 6}}
                initialNumToRender={10}
                data={spaces}
                keyExtractor={(item) => item.id}
                renderItem={renderSpace}
                refreshControl={<RefreshControl refreshing={isLoading} onRefresh={() => getSpaceData(language)}/>}
                numColumns={1}/>

            {SHOW_BANNER_AD && <BannerAd
                unitId={BANNER_ARTICLE_AD_UNIT_ID}
                size={BannerAdSize.ANCHORED_ADAPTIVE_BANNER}
                requestOptions={{
                    requestNonPersonalizedAdsOnly: true,
                }}
            />}
        </Background>
    );
}

export default ArticleCategoryScreen;

const styles = StyleSheet.create({
    languageText: {
        fontSize: 16
    },
    dropdown: {
        marginHorizontal: 14,
        width: 130,
        height: 40,
        borderBottomColor: 'gray',
        borderBottomWidth: 0.5,
    },
    imageStyle: {
        width: 15,
        height: 15,
    },
    selectedTextStyle: {
        fontSize: 16,
        marginLeft: 8,
    }
});
