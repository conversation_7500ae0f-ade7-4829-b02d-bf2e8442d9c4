import {ScrollView, StyleSheet, Text, useWindowDimensions, View} from 'react-native';
import {Colors} from '../constants/styles';
import Button from '../ui/Button';
import RenderHtml from "react-native-render-html";
import {newHtmlProps} from "../util/RenderHtmlRenfers";
import * as React from 'react';

function TestDescriptionForm({description, startTestOnPress, resumeTestOnPress, haveSavedTest}) {
    const width = useWindowDimensions().width;
    const MemoizedRenderHtml = React.memo(RenderHtml);

    const tagsStyles = {
        body: {
            fontSize: 16,
            lineHeight: 25,
            textAlign: 'justify'
        },
        img: {
            maxWidth: width * 0.9,
            height: 'auto'
        },
        p: {
            marginVertical: 5
        },
        ol: {
            marginVertical: 5
        },
        iframe: {
            opacity: 0.99
        },
        // If you are using @native-html/table-plugin
        table: {
            opacity: 0.99
        }
    };

    return (
        <View style={styles.container}>
            <View style={styles.instructionContainer}>
                <Text style={styles.instruction}>Instructions and Disclaim</Text>
            </View>
            <ScrollView>
                <MemoizedRenderHtml
                    contentWidth={width}
                    tagsStyles={tagsStyles}
                    source={{html: description}}
                    {...newHtmlProps}
                />
            </ScrollView>
            <View style={styles.buttonContainer}>
                <Button style={styles.button} onPress={startTestOnPress}>{haveSavedTest ? "New Test" : "Start Test"}</Button>

                {haveSavedTest && <Button style={[styles.button, {marginLeft: 10, backgroundColor: Colors.primary900}]}
                                          onPress={resumeTestOnPress}>Resume Test</Button>}
            </View>
        </View>
    );
}

export default TestDescriptionForm;

const styles = StyleSheet.create({
    container: {
        marginTop: 10,
        flex: 1
    },
    instructionContainer: {
        justifyContent: 'center',
        alignItems: 'center',
        marginVertical: 15
    },
    instruction: {
        color: Colors.accent100,
        fontWeight: 'bold',
        fontSize: 16
    },
    description: {
        color: Colors.accent100,
        fontSize: 15,
        textAlign: "justify"
    },
    buttonContainer: {
        flexDirection: 'row',
        marginVertical: 15,
        justifyContent: 'center',
        alignItems: 'center'
    },
    button: {
        width: 155,
        height: 40,
        justifyContent: 'center',
        alignItems: 'center'
    },
})
