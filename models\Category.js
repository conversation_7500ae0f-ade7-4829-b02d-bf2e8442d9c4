class Category {
  constructor(code, header, title, imageUri, numFullTest, numMiniTest, offer, entitlement,
              isPurchased, fromTime, toTime) {
    this.title = title;
    this.code = code;
    this.header = header;
    this.imageUri = imageUri;
    this.numFullTest = numFullTest;
    this.numMiniTest = numMiniTest;
    this.offer = offer;
    this.entitlement = entitlement;
    this.purchasedInfo = {
      isPurchased: isPurchased === null ? false : isPurchased,
      fromTime: fromTime,
      toTime: toTime
    }
  }
}

export default Category;
