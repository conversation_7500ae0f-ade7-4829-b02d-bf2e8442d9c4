class Test {
  constructor(id, code, categoryId, typeTestId, title, time, numQuestion, numWorkedQuestion, description,
              showExplain, answeredPoints, totalPoints, pass) {
    this.id = id;
    this.code = code;
    this.categoryId = categoryId;
    this.typeTestId = typeTestId;
    this.title = title;
    this.time = time;
    this.numQuestion = numQuestion;
    this.numWorkedQuestion = numWorkedQuestion;
    this.description = description;
    this.questions = null;
    this.showExplain = showExplain;
    this.answeredPoints = answeredPoints;
    this.totalPoints = totalPoints;
    this.pass = pass;
  }
}

export default Test;
