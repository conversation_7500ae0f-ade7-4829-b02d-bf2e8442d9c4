export function removeTags(str) {
  if ((str === null) || (str === '')) {
    return false;
  } else {
    str = str.toString();
  }

  // Regular expression to identify HTML tags in
  // the input string. Replacing the identified
  // HTML tag with a null string.
  return str.replace(/(<([^>]+)>)/ig, '');
}

export function secondToTime(seconds) {
  const hour = Math.floor(seconds / 3600);
  const minute = Math.floor((seconds - hour * 3600) / 60);
  const sec = seconds - hour * 3600 - minute * 60;

  return displayTwoDigit(hour) + ':' + displayTwoDigit(minute) + ':' + displayTwoDigit(sec);
}

function displayTwoDigit(value) {
  return value < 10 ? ('0' + value) : value;
}

export function hasNewVersion(currentBuildNumber, latestBuildNumber) {
  try {
    if (!latestBuildNumber) {
      return false;
    }
    const currentArray = currentBuildNumber.split(".");
    const latestArray = latestBuildNumber.split(".");

    if (currentArray.length === latestArray.length) {
      for (let i = 0; i < currentArray.length; i++) {
        if (parseInt(currentArray[i]) > parseInt(latestArray[i])) {
          return false;
        } else if (parseInt(currentArray[i]) < parseInt(latestArray[i])) {
          return true;
        }
      }
    }
  } catch (e) {
    console.log(e);
  }
  return false;
}

function treatAsUTC(date) {
  const result = date instanceof Date ? date : new Date(date);
  result.setMinutes(result.getMinutes() - result.getTimezoneOffset());
  return result;
}

export function daysBetween(startDate, endDate) {
  const millisecondsPerDay = 24 * 60 * 60 * 1000;
  return Math.ceil((treatAsUTC(endDate) - treatAsUTC(startDate)) / millisecondsPerDay);
}
