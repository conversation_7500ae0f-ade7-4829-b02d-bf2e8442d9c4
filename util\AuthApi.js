import axios from 'axios';
import {BE_URL} from '../constants/constants';

export async function login(email, password) {
  const url = BE_URL + "/authenticate";
  const response = await axios.post(url, {
    "username": email.trim(),
    "password": password.trim()
  });
  return response.data.data.jwtToken;
}

export async function generateVerification(email, type, userExisted) {
  const url = BE_URL + "/verification-code";
  const response = await axios.post(url, {
    "email": email.trim(),
    "type": type,
    "userExisted": userExisted
  });
  console.log(response.data);
}
