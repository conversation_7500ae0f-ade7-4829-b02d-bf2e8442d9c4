import {StyleSheet, Text, View} from "react-native";
import {<PERSON><PERSON><PERSON><PERSON>, Feather, MaterialCommunityIcons} from "@expo/vector-icons";
import GridTile from "./GridTile";
import {Colors} from "../constants/styles";

function ArticleGridTile({onPress, style, isFinished, isBookmarked, title}) {
    return (
        <GridTile onPress={onPress} style={{height: '100%', ...style, ...null}}>
            <View style={[styles.itemContainer]}>
                {isBookmarked &&
                    <View style={styles.bookmark}>
                        <MaterialCommunityIcons
                            name="bookmark-check-outline"
                            color={Colors.primary100}
                            size={18} />
                    </View>
                }
                <Text style={styles.title}>{title}</Text>
                {isFinished &&
                    <Feather name="check" size={18} color={Colors.primary100} />
                }
                <EvilIcons name="chevron-right" size={24} color="black"/>
            </View>
        </GridTile>
    );
}

export default ArticleGridTile;

const styles = StyleSheet.create({
    itemContainer: {
        width: '100%',
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 12,
        paddingHorizontal: 8
    },
    title: {
        flex: 1,
        fontSize: 14,
        fontWeight: 'bold',
        color: Colors.accent100,
        marginRight: 10
    },
    bookmark: {
        position: 'absolute',
        right: -2,
        top: -3
    }
});
