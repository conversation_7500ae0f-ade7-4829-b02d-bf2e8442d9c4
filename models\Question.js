class Question {
  // constructor(id, quizId, sort, title, points, question, correctMsg, incorrectMsg, correctSameText, tipEnabled,
  // tipMsg, answerType, showPointsInBox, answerPointsActivated, categoryId, answerPointsDiffModusActivated,
  // disableCorrect, matrixSortAnswerCriteriaWidth, answerData) { this.id = id; this.quizId = quizId; this.sort = sort;
  // this.title = title; this.points = points; this.question = question; this.correctMsg = correctMsg;
  // this.incorrectMsg = incorrectMsg; this.correctSameText = correctSameText; this.tipEnabled = tipEnabled;
  // this.tipMsg = tipMsg; this.answerType = answerType; this.showPointsInBox = showPointsInBox;
  // this.answerPointsActivated = answerPointsActivated; this.categoryId = categoryId;
  // this.answerPointsDiffModusActivated = answerPointsDiffModusActivated; this.disableCorrect = disableCorrect;
  // this.matrixSortAnswerCriteriaWidth = matrixSortAnswerCriteriaWidth; this.answerData = answerData; }

  constructor() {
  }
}

export default Question;
