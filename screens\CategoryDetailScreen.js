import {<PERSON><PERSON>, FlatList, Platform, StyleSheet, Text, View} from 'react-native';
import {TEST_TYPES} from '../data/DummyData';
import CategoryDetailGridTile from '../components/CategoryDetailGridTile';
import {useEffect, useLayoutEffect, useState} from 'react';
import {BannerAd, BannerAdSize} from 'react-native-google-mobile-ads';
import {
  BANNER_AD_UNIT_ID,
  ENABLE_REVENUE_CAT,
  SHOW_BANNER_AD,
  TYPE_TEST_MINI_CODE
} from '../constants/constants';
import Purchases from "react-native-purchases";
import LoadingOverlay from "../ui/LoadingOverlay";
import {Colors} from "../constants/styles";
import {Feather} from "@expo/vector-icons";
import {daysBetween} from "../util/Utils";
import moment from "moment/moment";
import {useIsFocused} from "@react-navigation/native";

function CategoryDetailScreen({navigation, route}) {
  const catCode = route.params.categoryCode;
  const catOffer = route.params.categoryOffer;
  const catEntitlement = route.params.categoryEntitlement;
  const catTitle = route.params.categoryTitle;
  const catHeader = route.params.categoryHeader;
  const numberOfQuiz = route.params.numberOfQuiz;
  const purchasedInfoApi = route.params.purchasedInfoApi;

  const [customer, setCustomer] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isValidPurchased, setIsValidPurchased] = useState(false);
  const [remainDays, setRemainDays] = useState(0);

  const isFocused = useIsFocused();

  const entitlement = catEntitlement ? catEntitlement : ('ez_' + catCode.toLowerCase());
  const offer = catOffer ? catOffer : ('ez_' + catCode.toLowerCase());

  useLayoutEffect(() => {
    navigation.setOptions({
      title: catHeader ? catHeader : catTitle
    });
  }, [catHeader, catTitle, navigation]);

  useEffect(() => {
    if (isFocused) {
      loadPurchaseData();
    }
  }, [isFocused]);

  async function loadPurchaseData() {
    try {
      setIsLoading(true);

      const purchasedInfoFromApi = getPurchaseInfoFromApiData();
      const purchasedInfoFromStore = await getCustomerInfo();
      console.info("purchasedInfoFromApi:", purchasedInfoFromApi, ";purchasedInfoFromStore:",
        purchasedInfoFromStore);

      const isPurchased = purchasedInfoFromApi.isPurchased || purchasedInfoFromStore.isPurchased;
      const remain = Math.max(purchasedInfoFromApi.remainDays, purchasedInfoFromStore.remainDays);

      setIsValidPurchased(isPurchased && (remain > 0 || isNaN(remain)));
      setRemainDays(remain);
    } finally {
      setIsLoading(false);
    }
  }

  function getPurchaseInfoFromApiData() {
    let isPurchasedFromApi = false;
    let remainDaysFromApi = 0;
    try {
      console.log(purchasedInfoApi);
      if (purchasedInfoApi && purchasedInfoApi.isPurchased) {
        const currTime = new Date().getTime();
        console.log("currTime: " + currTime);

        if (purchasedInfoApi.fromTime === null
            || purchasedInfoApi.fromTime <= 0
            || currTime > purchasedInfoApi.fromTime) {
          if (purchasedInfoApi.toTime === null
              || purchasedInfoApi.toTime <= 0
              || currTime < purchasedInfoApi.toTime) {
            isPurchasedFromApi = true;

            let daysBetweenValue;
            if (purchasedInfoApi.toTime === null || purchasedInfoApi.toTime <= 0) {
              daysBetweenValue = NaN;
            } else {
              daysBetweenValue = daysBetween(new Date(), purchasedInfoApi.toTime);
            }
            remainDaysFromApi = daysBetweenValue <= 0 ? 0 : daysBetweenValue;
          }
        }
      }
    } catch (e) {
      console.error(e);
    }
    return {
      isPurchased: isPurchasedFromApi,
      remainDays: remainDaysFromApi
    }
  }

  async function getCustomerInfo() {
    let isPurchasedFromStore = false;
    let remainDaysFromStore = 0;
    try {
      if (ENABLE_REVENUE_CAT) {
        const customerInfo = await Purchases.getCustomerInfo();
        // console.info(customerInfo);

        const entitlementActive = customerInfo.entitlements.active[entitlement];
        if (typeof entitlementActive !== 'undefined') {
          console.info(entitlementActive);
          isPurchasedFromStore = true;

          let expirationDate;
          if (entitlementActive.expirationDate) {
            expirationDate = new Date(entitlementActive.expirationDate);
          } else {
            const latestPurchaseDate = new Date(entitlementActive.latestPurchaseDate);
            const productSku = entitlementActive.productIdentifier;
            let subscriptionDays = productSku.split('_').pop();
            subscriptionDays = subscriptionDays ? subscriptionDays : 0;

            expirationDate = moment(latestPurchaseDate).add(subscriptionDays, 'day').toDate();
          }

          const daysBetweenValue = daysBetween(new Date(), expirationDate);
          remainDaysFromStore = daysBetweenValue <= 0 ? 0 : daysBetweenValue;
        }

        setCustomer(customerInfo);
      }
    } catch (e) {
      console.error(e);
    }
    return {
      isPurchased: isPurchasedFromStore,
      remainDays: remainDaysFromStore
    }
  }

  function renderCategoryDetailItem(itemData) {
    async function pressHandler() {
      try {
        if (isValidPurchased || itemData.item.code === TYPE_TEST_MINI_CODE) {
          navigation.navigate("Tests", {
            categoryCode: catCode,
            categoryTitle: catTitle,
            categoryHeader: catHeader,
            typeTestId: itemData.item.id,
            typeTestTitle: itemData.item.title,
            typeTestCode: itemData.item.code,
            isPurchased: isValidPurchased
          });
        } else {
          navigation.navigate('Paywall', {
            offer: offer,
            entitlement: entitlement,
            title: "You have to purchase to access the full test. Here are options you can try"
          });
        }
      } catch (e) {
        Alert.alert('Error fetching customer info', e.message);
      }
    }

    return <CategoryDetailGridTile
      title={itemData.item.title}
      remainDay={remainDays}
      isMini={itemData.item.code === TYPE_TEST_MINI_CODE}
      numQuiz={numberOfQuiz[itemData.item.code]}
      style={{marginLeft: itemData.index % 2 === 0 ? 0 : 12}}
      onPress={pressHandler} />;
  }

  if (isLoading) {
    return <LoadingOverlay message={'Loading...'} />;
  }

  return (
    <>
      <View style={{flex: 1}}>
        <FlatList style={{paddingHorizontal: 14, flexGrow: 0, paddingTop: 10, paddingBottom: 10}}
                  data={TEST_TYPES}
                  keyExtractor={(item) => item.id}
                  renderItem={renderCategoryDetailItem}
                  numColumns={2} />

        <View style={styles.description}>
          <View style={styles.textRow}>
            <Feather name="arrow-right-circle" size={20} color={Colors.primary100} />
            <Text style={styles.text}>
              The number questions of a
              <Text style={{fontWeight: "bold"}}> Mini Test </Text>
              is approximately 30% the one of a
              <Text style={{fontWeight: "bold"}}> Full Test.</Text>
            </Text>
          </View>

          <View style={styles.textRow}>
            <Feather name="arrow-right-circle" size={20} color={Colors.primary100} />
            <Text style={styles.text}>When you choose the Full Test, you will get many advantages:</Text>
          </View>

          <View style={[styles.textRow, {marginStart: 25}]}>
            <Feather name="arrow-right-circle" size={12} color={Colors.primary100} />
            <Feather name="arrow-right-circle" size={12} color={Colors.primary100} />
            <Text style={styles.text}>The Full Test simulates like the real exam.</Text>
          </View>

          <View style={[styles.textRow, {marginStart: 25}]}>
            <Feather name="arrow-right-circle" size={12} color={Colors.primary100} />
            <Feather name="arrow-right-circle" size={12} color={Colors.primary100} />
            <Text style={styles.text}>You can get results and detailed explanation of the results.</Text>
          </View>

          <View style={[styles.textRow, {marginStart: 25}]}>
            <Feather name="arrow-right-circle" size={12} color={Colors.primary100} />
            <Feather name="arrow-right-circle" size={12} color={Colors.primary100} />
            <Text style={styles.text}>NO Advertising.</Text>
          </View>
        </View>
      </View>

      {SHOW_BANNER_AD && !isValidPurchased &&
       <BannerAd
         unitId={BANNER_AD_UNIT_ID}
         size={BannerAdSize.ANCHORED_ADAPTIVE_BANNER}
         requestOptions={{
           requestNonPersonalizedAdsOnly: true,
         }}
       />}
    </>
  );
}

export default CategoryDetailScreen;

const styles = StyleSheet.create({
  description: {
    flexDirection: "column",
    borderWidth: 1,
    borderColor: Colors.primary100,
    marginHorizontal: 14,
    paddingHorizontal: 5,
    backgroundColor: 'white',
    paddingTop: 10
  },
  textRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 10
  },
  text: {
    marginStart: 5,
    flexWrap: "wrap",
    flexShrink: 1,
    fontSize: 13,
    textAlign: 'justify'
  }
});
