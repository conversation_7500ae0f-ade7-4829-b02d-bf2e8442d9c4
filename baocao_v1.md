# BÁO CÁO PHÂN TÍCH DỰ ÁN EZAMI - PHIÊN BẢN CHI TIẾT

## Tổng quan dự án
**Ezami** là ứng dụng giáo dục di động được xây dựng bằng React Native và Expo, cung cấp hệ thống thi trắc nghiệm tương tác và quản lý nội dung giáo dục với tích hợp thanh toán và quảng cáo.

---

# 1. Cấu trúc dự án

```
ezami-app/
├── App.js                          # Entry point chính, cấu hình navigation và providers
├── README.md                       # Tài liệu hướng dẫn dự án
├── app.json                        # Cấu hình Expo app
├── babel.config.js                 # Cấu hình Babel transpiler
├── package.json                    # Dependencies và scripts
├── package-lock.json               # Lock file cho dependencies
├── eas.json                        # Cấu hình EAS Build
├── firebase.json                   # Cấu hình Firebase services
├── assets/                         # Thư mục chứa tài nguyên tĩnh
│   ├── adaptive-icon.png           # Icon adaptive cho Android
│   ├── avatar.png                  # Avatar mặc định
│   ├── background.png              # Background image
│   ├── icon.png                    # App icon chính
│   ├── splash.png                  # Splash screen image
│   ├── config/                     # Cấu hình assets
│   ├── flags/                      # Icons cờ quốc gia
│   └── onboarding/                 # Images cho onboarding
├── components/                     # Thư mục chứa các React components tái sử dụng
│   ├── AnswerItem.js               # Component hiển thị từng đáp án trong quiz
│   ├── ArticleCategoryGridTile.js  # Grid tile cho category bài viết
│   ├── ArticleGridTile.js          # Grid tile cho bài viết
│   ├── ArticleListComponent.js     # List component cho bài viết
│   ├── CategoryGridTile.js         # Grid tile cho category quiz
│   ├── CategoryDetailGridTile.js   # Grid tile cho chi tiết category
│   ├── LoginForm.js                # Form đăng nhập/đăng ký
│   ├── PackageItem.js              # Component hiển thị gói subscription
│   ├── Player.js                   # Media player component
│   ├── QuestionGridTile.js         # Grid tile cho câu hỏi
│   ├── ResultForm.js               # Form hiển thị kết quả quiz
│   ├── TestDetailBody.js           # Body content của test detail
│   ├── TestDetailFooter.js         # Footer navigation của test detail
│   ├── TestDetailHeader.js         # Header của test detail
│   ├── TestGridTile.js             # Grid tile cho test
│   ├── TestDescriptionForm.js      # Form mô tả test
│   └── YoutubPlayer.js             # YouTube player component
├── constants/                      # Thư mục chứa các hằng số và cấu hình
│   ├── constants.js                # Các hằng số chính (API URLs, Ad IDs, etc.)
│   ├── styles.js                   # Style constants và themes
│   ├── TestStatus.js               # Enum trạng thái test
│   └── VerificationType.js         # Enum loại verification
├── data/                           # Thư mục chứa dữ liệu tĩnh và dummy data
│   └── DummyData.js                # Dữ liệu mẫu cho development
├── models/                         # Thư mục chứa các data models
│   ├── Answer.js                   # Model cho đáp án
│   ├── Article.js                  # Model cho bài viết
│   ├── ArticleCategory.js          # Model cho category bài viết
│   ├── ArticleSpace.js             # Model cho space bài viết
│   ├── Category.js                 # Model cho category quiz
│   ├── CheckVersion.js             # Model cho version checking
│   ├── Question.js                 # Model cho câu hỏi
│   ├── ResultResponse.js           # Model cho kết quả quiz
│   ├── Setting.js                  # Model cho settings
│   ├── Test.js                     # Model cho test/quiz
│   ├── TestType.js                 # Model cho loại test
│   └── Version.js                  # Model cho version info
├── patches/                        # Thư mục chứa patches cho third-party packages
│   └── react-native+0.71.14.patch # Patch cho React Native
├── screens/                        # Thư mục chứa các màn hình chính
│   ├── AccountSettingScreen.js     # Màn hình cài đặt tài khoản
│   ├── ArticleCategoryScreen.js    # Màn hình danh sách category bài viết
│   ├── ArticleDetailScreen.js      # Màn hình chi tiết bài viết
│   ├── ArticlesScreen.js           # Màn hình danh sách bài viết
│   ├── CategoryDetailScreen.js     # Màn hình chi tiết category quiz
│   ├── CategoryScreen.js           # Màn hình danh sách category quiz
│   ├── ChangePasswordScreen.js     # Màn hình đổi mật khẩu
│   ├── ContactScreen.js            # Màn hình liên hệ
│   ├── ForgetPasswordScreen.js     # Màn hình quên mật khẩu
│   ├── LoginScreen.js              # Màn hình đăng nhập
│   ├── OnboardingScreen.js         # Màn hình hướng dẫn người dùng mới
│   ├── PaywallScreen.js            # Màn hình thanh toán subscription
│   ├── RegisterScreen.js           # Màn hình đăng ký
│   ├── SettingScreen.js            # Màn hình cài đặt chung
│   ├── SoftwareInfoScreen.js       # Màn hình thông tin phần mềm
│   ├── TestDetailScreen.js         # Màn hình làm bài thi chi tiết
│   └── TestsScreen.js              # Màn hình danh sách bài thi
├── store/                          # Thư mục chứa state management (Context API)
│   ├── AppContext.js               # Context quản lý state app global
│   ├── AuthContext.js              # Context quản lý authentication
│   └── QuizContext.js              # Context quản lý state quiz/test
├── ui/                             # Thư mục chứa các UI components cơ bản
│   ├── Background.js               # Background component
│   ├── Button.js                   # Button component tùy chỉnh
│   ├── ConfirmationInput.js        # Input component cho confirmation code
│   ├── DatePicker.js               # Date picker component
│   ├── IconButton.js               # Button với icon
│   ├── IconInput.js                # Input với icon
│   ├── IconRoundButton.js          # Round button với icon
│   ├── Input.js                    # Input component cơ bản
│   ├── LoadingOverlay.js           # Loading overlay component
│   └── TextNote.js                 # Text note component
└── util/                           # Thư mục chứa utility functions và API calls
    ├── AuthApi.js                  # API calls cho authentication
    ├── DateUtils.js                # Utility functions cho date/time
    ├── PostApi.js                  # API calls cho articles/posts
    ├── QuizApi.js                  # API calls cho quiz/test
    ├── RenderHtmlRenfers.js        # Utility cho render HTML content
    ├── UserApi.js                  # API calls cho user management
    ├── Utils.js                    # Utility functions chung
    └── VersionApi.js               # API calls cho version checking
```

---

# 2. Danh sách tính năng

## Tính năng dành cho User
- **Authentication & Account Management**: Đăng nhập, đăng ký, quên mật khẩu, quản lý thông tin cá nhân
- **Quiz/Test System**: Làm bài thi trắc nghiệm với nhiều loại câu hỏi, lưu tiến độ, xem kết quả
- **Article Reading System**: Đọc bài viết giáo dục, bookmark, theo dõi tiến độ đọc
- **Category Browsing**: Duyệt danh mục bài thi và bài viết theo chủ đề
- **Offline Learning**: Lưu tiến độ học offline, đồng bộ khi có internet
- **Progress Tracking**: Theo dõi điểm số, thời gian làm bài, tỷ lệ đúng/sai
- **Premium Subscription**: Mua gói premium để truy cập full content
- **Settings & Preferences**: Cài đặt ứng dụng, thay đổi thông tin cá nhân
- **Onboarding Experience**: Hướng dẫn sử dụng app cho người dùng mới

## Tính năng dành cho Admin (Backend)
- **Content Management**: Quản lý câu hỏi, bài thi, bài viết
- **User Management**: Quản lý tài khoản người dùng, phân quyền
- **Analytics & Reporting**: Theo dõi hoạt động người dùng, thống kê
- **Version Control**: Quản lý phiên bản app, thông báo cập nhật
- **Subscription Management**: Quản lý gói subscription, thanh toán
- **Category Management**: Quản lý danh mục bài thi và bài viết

---

# 3. Chi tiết tính năng và file liên quan

## Feature 1 (User): Authentication & Account Management
- **screens/LoginScreen.js**: Giao diện đăng nhập, xử lý login flow
- **screens/RegisterScreen.js**: Giao diện đăng ký tài khoản mới
- **screens/ForgetPasswordScreen.js**: Giao diện reset mật khẩu
- **screens/AccountSettingScreen.js**: Quản lý thông tin tài khoản
- **screens/ChangePasswordScreen.js**: Đổi mật khẩu
- **components/LoginForm.js**: Form component tái sử dụng cho login/register
- **store/AuthContext.js**: Quản lý state authentication, JWT token
- **util/AuthApi.js**: API calls cho authentication (login, verification)
- **util/UserApi.js**: API calls cho user management (update info, change password)

## Feature 2 (User): Quiz/Test System
- **screens/TestDetailScreen.js**: Màn hình chính làm bài thi, quản lý state test
- **screens/TestsScreen.js**: Danh sách bài thi theo category
- **components/TestDetailHeader.js**: Header hiển thị timer, progress
- **components/TestDetailBody.js**: Hiển thị câu hỏi và đáp án
- **components/TestDetailFooter.js**: Navigation giữa các câu hỏi
- **components/AnswerItem.js**: Component cho từng đáp án
- **components/ResultForm.js**: Hiển thị kết quả sau khi hoàn thành
- **components/TestGridTile.js**: Grid item cho danh sách test
- **store/QuizContext.js**: Quản lý state quiz, lưu tiến độ offline
- **util/QuizApi.js**: API calls cho quiz (get questions, submit answers)
- **models/Test.js, Question.js, Answer.js**: Data models cho quiz system

## Feature 3 (User): Article Reading System
- **screens/ArticleCategoryScreen.js**: Danh sách categories bài viết
- **screens/ArticlesScreen.js**: Danh sách bài viết theo category
- **screens/ArticleDetailScreen.js**: Đọc bài viết chi tiết với HTML rendering
- **components/ArticleListComponent.js**: List item cho bài viết
- **components/ArticleGridTile.js**: Grid item cho bài viết
- **util/PostApi.js**: API calls cho articles (get spaces, posts, post detail)
- **models/Article.js, ArticleCategory.js, ArticleSpace.js**: Data models cho article system

## Feature 4 (User): Premium Subscription
- **screens/PaywallScreen.js**: Màn hình thanh toán subscription
- **components/PackageItem.js**: Component hiển thị gói subscription
- **screens/CategoryDetailScreen.js**: Kiểm tra subscription status
- **constants/constants.js**: Cấu hình RevenueCat API keys

## Feature 5 (User): Settings & Preferences
- **screens/SettingScreen.js**: Màn hình cài đặt chính
- **screens/ContactScreen.js**: Thông tin liên hệ
- **screens/SoftwareInfoScreen.js**: Thông tin phần mềm
- **store/AppContext.js**: Quản lý app settings
- **data/DummyData.js**: Dữ liệu settings options

---

# 4. Flow code theo function

### File: store/AuthContext.js
#### Function: authentication
- **Chức năng**: Xác thực và lưu trữ JWT token sau khi đăng nhập thành công
- **Flow xử lý**:
  1. Tạo object auth chứa token và email
  2. Cập nhật state với token mới
  3. Lưu token vào AsyncStorage để persist offline

```javascript
async function authentication(token, email) {
  try {
    // Step 1: Tạo object auth chứa token và email từ response
    const auth = {
      'token': token,
      'email': email
    };
    // Step 2: Cập nhật state AuthContext với token mới
    setAuthToken(auth);
    // Step 3: Lưu token vào AsyncStorage để persist khi app restart
    await AsyncStorage.setItem('token', JSON.stringify(auth));
  } catch (ex) {
    console.log(ex)
  }
}
```

#### Function: isAuthenticated
- **Chức năng**: Kiểm tra tính hợp lệ của JWT token hiện tại
- **Flow xử lý**:
  1. Kiểm tra token và email có tồn tại
  2. Decode JWT token để lấy expiration time
  3. So sánh với thời gian hiện tại để validate

```javascript
function isAuthenticated() {
  try {
    // Step 1: Kiểm tra authToken object có đầy đủ token và email
    if (authToken && authToken.token && authToken.email) {
      // Step 2: Decode JWT token để lấy thông tin expiration
      let decodedToken = jwt_decode(authToken.token);
      let currentDate = new Date();

      // Step 3: Kiểm tra token có hết hạn chưa (JWT exp tính bằng seconds)
      if (decodedToken.exp * 1000 < currentDate.getTime() - 86400 * 1000) {
        console.warn("Token expired.");
        return false;
      } else {
        console.info("Valid token");
        return true;
      }
    } else {
      return false;
    }
  } catch (ex) {
    console.log(ex);
  }
  return false;
}
```

### File: util/QuizApi.js
#### Function: getQuestions
- **Chức năng**: Lấy danh sách câu hỏi và đáp án cho một bài thi cụ thể
- **Flow xử lý**:
  1. Chuẩn bị headers với Bearer token
  2. Gọi API endpoint để lấy questions
  3. Parse response data thành Question objects
  4. Tạo Test object hoàn chỉnh với questions

```javascript
export async function getQuestions(authCtx, quizId) {
  try {
    // Step 1: Chuẩn bị headers với Bearer token cho authentication
    const headers = {Authorization: `Bearer ${authCtx.token}`};

    // Step 2: Gọi API endpoint để lấy questions của quiz cụ thể
    const response = await axios.get(BE_URL + "/api/quiz/" + quizId + "/question", {headers});

    const data = [];
    // Step 3: Loop qua response data để parse thành Question objects
    for (const item of response.data.data) {
      const question = new Question();
      question.id = item.id;
      question.quizId = item.quizId;
      question.sort = item.sort;
      // Map các properties khác từ API response

      // Step 4: Parse answer data cho từng câu hỏi
      const answerData = [];
      for (const answer of item.answerData) {
        answerData.push(new Answer(answer.id, answer.answer, answer.correct, answer.points));
      }
      question.answerData = answerData;
      data.push(question);
    }

    // Step 5: Tạo Test object hoàn chỉnh với questions đã parse
    const test = new Test(response.data.data.id, response.data.data.slug, null, null,
      response.data.data.name, response.data.data.timeLimit, data.length,
      null, response.data.data.postContent, response.data.data.showExplain);
    test.questions = data;

    return test;
  } catch (e) {
    // Step 6: Xử lý exception, gọi handleException để logout nếu 401
    handleException(e, authCtx);
  }
  return null;
}
```

#### Function: submitAnswerData
- **Chức năng**: Gửi đáp án đã chọn lên server và nhận kết quả bài thi
- **Flow xử lý**:
  1. Chuẩn bị URL và headers
  2. Convert Map answerData thành format phù hợp cho API
  3. Gửi POST request với timing data
  4. Parse response thành ResultResponse object

```javascript
export async function submitAnswerData(token, quizId, answerData, startTime, endTime, elapsedTime) {
  // Step 1: Chuẩn bị URL endpoint và headers với token
  const url = BE_URL + "/api/quiz/" + quizId;
  const headers = {Authorization: `Bearer ${token}`};

  // Step 2: Convert Map answerData thành array format cho API
  const data = [];
  answerData.forEach(function (value, key) {
    data.push({
      "questionId": key,
      "answerData": value
    })
  })

  // Step 3: Gửi POST request với timing data và answers
  const response = await axios.post(url, {
    "startTime": startTime,
    "endTime": endTime,
    "elapsedTime": elapsedTime,
    "data": data
  }, {headers});

  // Step 4: Parse response thành ResultResponse object
  const item = response.data.data;
  return new ResultResponse(item.corrects, item.totalQuestion, item.answeredTime,
    item.point, item.totalPoint, item.percentage, item.passPercentage,
    item.pass, item.resultText);
}
```

### File: screens/TestDetailScreen.js
#### Function: submitAnswer
- **Chức năng**: Xử lý submit bài thi và hiển thị kết quả
- **Flow xử lý**:
  1. Disable autoplay và tính thời gian
  2. Gọi API submitAnswerData
  3. Cập nhật state với kết quả
  4. Chuyển sang view result mode

```javascript
async function submitAnswer() {
  setIsLoading(true);
  try {
    // Step 1: Disable autoplay audio/video và tính elapsed time
    quizCtx.disableAutoPlay();
    const elapsedTime = testTime.totalElapsedTime + testTime.groupElapsedTime;

    // Step 2: Gọi API submitAnswerData để gửi đáp án lên server
    const resultData = await submitAnswerData(authCtx.token, testId, answeredMap, startTime,
      Math.floor(Date.now() / 1000), elapsedTime);

    // Step 3: Cập nhật state với kết quả từ server
    setResultResponse(resultData);
    setTestStatus(TestStatus.FINISHED);

    // Step 4: Reset về câu hỏi đầu tiên và gọi success handler
    nextQuestion(0);
    submitSuccessHandler();
  } catch (e) {
    console.log(e);
    Alert.alert("Error", "An error occurred, please try again later");
  } finally {
    setIsLoading(false);
  }
}
```

#### Function: resumeTestHandler
- **Chức năng**: Khôi phục bài thi đã lưu từ local storage
- **Flow xử lý**:
  1. Load dữ liệu quiz đã lưu từ QuizContext
  2. Restore state từ saved data
  3. Tiếp tục từ câu hỏi đã dừng

```javascript
async function resumeTestHandler() {
  setIsLoading(true);
  // Step 1: Load saved quiz data từ QuizContext (AsyncStorage)
  const savedQuizData = await quizCtx.loadQuiz(testId);

  if (savedQuizData) {
    // Step 2: Restore test time từ saved data
    const savedTestTime = savedQuizData.testTime;
    savedTestTime.remainTime = savedTestTime.countDown;
    setTestTime(savedTestTime);

    // Step 3: Restore test data và answered map
    setDisplayedTest(savedQuizData.testData);
    setAnsweredMap(savedQuizData.answeredMap);
    setStartTime(savedQuizData.startTime);
    setResultResponse(null);

    // Step 4: Navigate đến câu hỏi đã dừng và start test
    nextQuestion(savedQuizData.currQuestionIdx);
    setQuestionNumber(savedQuizData.questionNumber);

    setTimeout(() => {
      setTestStatus(TestStatus.STARTED);
      setIsLoading(false);
    }, 1000);
  }
}
```

### File: store/QuizContext.js
#### Function: saveQuiz
- **Chức năng**: Lưu tiến độ làm bài offline để có thể resume sau
- **Flow xử lý**:
  1. Serialize Map objects thành JSON format
  2. Lưu vào AsyncStorage với key unique

```javascript
async function saveQuiz(quizId, quizProgressData) {
  try {
    // Step 1: Convert Map objects thành JSON serializable format using replacer
    // Step 2: Lưu vào AsyncStorage với key unique cho từng quiz
    await AsyncStorage.setItem('quiz_data_' + quizId, JSON.stringify(quizProgressData, replacer));
  } catch (ex) {
    console.log(ex)
  }
}

// Helper function để serialize Map objects
function replacer(key, value) {
  if(value instanceof Map) {
    return {
      dataType: 'Map',
      value: Array.from(value.entries()),
    };
  } else {
    return value;
  }
}
```

### File: util/PostApi.js
#### Function: getArticleSpaces
- **Chức năng**: Lấy danh sách spaces và categories của articles
- **Flow xử lý**:
  1. Gọi API với language parameter
  2. Parse response thành ArticleSpace objects
  3. Map categories cho mỗi space

```javascript
export async function getArticleSpaces(authCtx, language) {
  try {
    // Step 1: Chuẩn bị headers và gọi API với language parameter
    const headers = {Authorization: `Bearer ${authCtx.token}`};
    const response = await axios.get(BE_URL + "/api/post/space?language=" + language, {headers});

    const data = [];
    // Step 2: Parse response data thành ArticleSpace objects
    for (const item of response.data.data) {
      const categories = [];

      // Step 3: Parse categories cho mỗi space
      for (const category of item.categories) {
        categories.push(new ArticleCategory(category.id, category.name));
      }

      // Step 4: Tạo ArticleSpace object với categories đã parse
      data.push(new ArticleSpace(item.id, item.title, categories));
    }
    return data;
  } catch (e) {
    // Step 5: Xử lý exception, logout nếu unauthorized
    handleException(e, authCtx);
  }
  return [];
}
```

### File: screens/PaywallScreen.js
#### Function: getPackages (useEffect)
- **Chức năng**: Lấy danh sách gói subscription từ RevenueCat
- **Flow xử lý**:
  1. Gọi RevenueCat API để lấy offerings
  2. Filter theo offer parameter
  3. Set packages state

```javascript
useEffect(() => {
  const getPackages = async () => {
    try {
      // Step 1: Gọi RevenueCat API để lấy tất cả offerings
      const offerings = await Purchases.getOfferings();

      // Step 2: Filter offering theo offer parameter từ route
      if (offerings.all[offer] && offerings.all[offer].availablePackages.length !== 0) {
        // Step 3: Set packages state với available packages
        setPackages(offerings.all[offer].availablePackages);
      }
    } catch (e) {
      console.error(e);
      Alert.alert('Error getting offers', e.message);
    }
  };

  getPackages();
}, []);
```

### File: util/UserApi.js
#### Function: updateUserInfo
- **Chức năng**: Cập nhật thông tin người dùng
- **Flow xử lý**:
  1. Chuẩn bị headers với token
  2. Gửi POST request với user data
  3. Return response data

```javascript
export async function updateUserInfo(token, newEmail, mobile, fullName, dob) {
  // Step 1: Chuẩn bị headers với Bearer token
  const headers = {Authorization: `Bearer ${token}`};
  const url = BE_URL + "/api/user/update";

  // Step 2: Gửi POST request với user data đã trim
  const response = await axios.post(url, {
    "email": newEmail.trim(),
    "mobile": mobile ? mobile.trim() : "",
    "fullName": fullName.trim(),
    "dob": dob ? dob.trim() : ""
  }, {headers});

  // Step 3: Return response data
  return response.data;
}
```

---

## Kết luận

Dự án Ezami được thiết kế với kiến trúc modular rõ ràng, phân tách concerns tốt giữa UI components, business logic, và data management. Hệ thống authentication sử dụng JWT tokens với offline persistence, quiz system hỗ trợ save/resume functionality, và tích hợp đầy đủ payment system qua RevenueCat.

**Điểm mạnh:**
- Kiến trúc component-based với separation of concerns
- Offline-first approach với AsyncStorage
- Comprehensive error handling và user feedback
- Cross-platform support với React Native/Expo
- Modern state management với Context API

**Khuyến nghị:**
- Implement comprehensive unit testing
- Add TypeScript for better type safety
- Optimize performance với React.memo và useMemo
- Enhance accessibility features
