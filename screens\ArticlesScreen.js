import {<PERSON>List, RefreshControl, StyleSheet, Text, View} from "react-native";
import {BANNER_ARTICLE_AD_UNIT_ID, SHOW_BANNER_AD} from "../constants/constants";
import {BannerAd, BannerAdSize} from "react-native-google-mobile-ads";
import {useContext, useEffect, useLayoutEffect, useState} from "react";
import {AuthContext} from "../store/AuthContext";
import {getPosts} from "../util/PostApi";
import ArticleListComponent from "../components/ArticleListComponent";
import LoadingOverlay from "../ui/LoadingOverlay";
import {useIsFocused} from "@react-navigation/native";
import AsyncStorage from "@react-native-async-storage/async-storage";

function ArticlesScreen({navigation, route}) {
    const [articles, setArticles] = useState([]);
    const [articleIds, setArticleIds] = useState([]);
    const [isLoading, setIsLoading] = useState(true);

    const authCtx = useContext(AuthContext);
    const categoryId = route.params.categoryId;
    const categoryName = route.params.categoryName;

    const isFocused = useIsFocused();

    useEffect(() => {
        if (isFocused) {
            const hasArticles = !!articles && articles.length > 0;
            if (hasArticles) {
                setArticleStatus();
            } else {
                getArticles(categoryId);
            }
        }
    }, [isFocused]);

    useLayoutEffect(() => {
        navigation.setOptions({
            title: categoryName
        });
    }, [categoryName, navigation]);

    async function setArticleStatus() {
        try {
            const finishedArticleSet = await getFinishedArticles();
            const bookmarkArticleId = await AsyncStorage.getItem("bookmark_article_" + categoryId);
            setArticles([]);

            const articleList = articles;
            for (const article of articleList) {
                article.finished = finishedArticleSet.has(article.id);
                article.bookmarked = !!(bookmarkArticleId && article.id === +bookmarkArticleId);
            }
            setArticles(articleList);

        } finally {
            setIsLoading(false);
        }
    }

    async function getFinishedArticles() {
        const finishData = await AsyncStorage.getItem('finished_articles_' + categoryId);
        let finishedArticles = !!finishData ?JSON.parse(finishData) : [];
        return new Set(finishedArticles);
    }

    async function getArticles(categoryId) {
        try {
            setIsLoading(true);
            const articleList = await getPosts(authCtx, categoryId);
            const finishedArticleSet = await getFinishedArticles();
            const bookmarkArticleId = await AsyncStorage.getItem("bookmark_article_" + categoryId);
            for (const article of articleList) {
                article.finished = finishedArticleSet.has(article.id);
                article.bookmarked = !!(bookmarkArticleId && article.id === +bookmarkArticleId);
            }

            setArticles(articleList);
            setArticleIds(articleList.map((article) => {
                return article.id
            }));
        } catch (ex) {
            console.error(ex);
        } finally {
            setIsLoading(false);
        }
    }

    function renderArticleItem(itemData) {
        return <ArticleListComponent
            itemData={itemData}
            categoryName={categoryName}
            categoryId={categoryId}
            articleIds={articleIds}/>;
    }

    function keyExt(item) {
        return item.id;
    }

    if (isLoading) {
        return <LoadingOverlay message={'Loading...'}/>;
    }

    if (articles.length === 0) {
        return <View style={styles.container}>
            <Text style={styles.noDataText}>No Data</Text>
        </View>
    }

    return (
        <View style={{flex: 1}}>
            <FlatList contentContainerStyle={{paddingHorizontal: 14, paddingTop: 10, paddingBottom: 4}}
                      data={articles}
                      initialNumToRender={2}
                      windowSize={7}
                      maxToRenderPerBatch={1}
                      removeClippedSubviews={true}
                      updateCellsBatchingPeriod={100}
                      keyExtractor={keyExt}
                      renderItem={renderArticleItem}
                      refreshControl={<RefreshControl refreshing={isLoading}
                                                      onRefresh={() => getArticles(categoryId)}/>}
                      numColumns={1}/>

            {SHOW_BANNER_AD &&
                <BannerAd
                    unitId={BANNER_ARTICLE_AD_UNIT_ID}
                    size={BannerAdSize.ANCHORED_ADAPTIVE_BANNER}
                    requestOptions={{
                        requestNonPersonalizedAdsOnly: true,
                    }}
                />}
        </View>
    );
}

export default ArticlesScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center'
    },
    noDataText: {
        fontSize: 18
    }
})
