import SettingGridTile from '../components/SettingGridTile';
import {
  Alert,
  FlatList,
  Image,
  Text,
  Pressable,
  StyleSheet,
  View,
  Linking,
  TouchableOpacity
} from 'react-native';
import {SETTINGS} from '../data/DummyData';
import {AntDesign, Feather, MaterialCommunityIcons} from '@expo/vector-icons';
import {Colors} from '../constants/styles';
import {useContext, useState} from 'react';
import {AuthContext} from '../store/AuthContext';
import {BannerAd, BannerAdSize, TestIds} from 'react-native-google-mobile-ads';
import {BANNER_AD_UNIT_ID, SHOW_BANNER_AD} from '../constants/constants';
import LoadingOverlay from '../ui/LoadingOverlay';
import {deleteAccount} from '../util/UserApi';
import Dialog from "react-native-dialog";
import Clipboard from '@react-native-clipboard/clipboard';
import Toast from "react-native-toast-message";

function SettingScreen({navigation}) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [isShowDialog, setIsShowDialog] = useState(false);
  const authCtx = useContext(AuthContext);

  function editPressHandler() {
    navigation.navigate("AccountSetting");
  }

  function deleteAccountHandler() {
    Alert.alert(
      'Confirm account deletion',
      'Are you sure you want to delete your account?',
      [
        {
          text: 'Cancel',
          onPress: () => setIsDeleting(false),
          style: 'cancel',
        },
        {
          text: 'Delete',
          onPress: () => {
            setIsDeleting(true);
            doDeleteAccount();
          },
          style: 'destructive',
        },
      ],
    );
  }

  function doDeleteAccount() {
    deleteAccount(authCtx.token).then((response) => {
      setIsDeleting(false);
      if (response.code === 200) {
        authCtx.logout();
      } else {
        Alert.alert(
          "Fail",
          response.message,
          [{
            text: "OK"
          }]
        );
      }
    }).catch((error) => {
      setIsDeleting(false);
      console.log(error);
      Alert.alert(
        "Fail",
        "An error occurred, please try again later",
        [{
          text: "OK"
        }]
      );
    })
  }

  function logoutHandler() {
    Alert.alert(
      'Logout',
      'Are you sure you want to log out?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Log out',
          onPress: () => {
            authCtx.logout();
          },
          style: 'destructive',
        },
      ],
    );
  }

  function renderItem(itemData) {
    function pressHandler(type) {
      switch (type) {
        case 'software-info':
          navigation.navigate("SoftwareInfo");
          break;
        case 'change-pass':
          navigation.navigate("ChangePass");
          break;
        case 'update-account':
          navigation.navigate("AccountSetting");
          break;
        case 'contact-us':
          showDialog();
          break;
        case 'delete-account':
          deleteAccountHandler();
          break;
        case 'log-out':
          logoutHandler();
          break;
      }
    }

    return <SettingGridTile
      text={itemData.item.title}
      icon={itemData.item.icon}
      isRightIcon={itemData.item.isRightIcon}
      onPress={pressHandler.bind(this, itemData.item.code)} />;
  }

  const showDialog = () => {
    setIsShowDialog(true);
  };

  const handleCancel = () => {
    setIsShowDialog(false);
  };

  function copyEmail() {
    Clipboard.setString('<EMAIL>');
    Toast.show({type: 'success', text1: 'Copied!', visibilityTime: 2000});
  }

  if (isDeleting) {
    return <LoadingOverlay message={'Deleting...'} />;
  }

  return (
    <View style={styles.container}>
      <View style={styles.accountContainer}>
        <View style={styles.avatarContainer}>
          <Image style={styles.avatar} source={require('../assets/avatar.png')} />
          <Feather style={styles.camera} name="camera" size={24} color={Colors.primary100} />
        </View>
        <Pressable onPress={editPressHandler}>
          <AntDesign name="edit" size={24} color={Colors.primary100} />
        </Pressable>
      </View>
      <FlatList
        data={SETTINGS}
        keyExtractor={(item) => item.code}
        renderItem={renderItem}
        numColumns={1} />
      {SHOW_BANNER_AD && <BannerAd
        unitId={BANNER_AD_UNIT_ID}
        size={BannerAdSize.ANCHORED_ADAPTIVE_BANNER}
        requestOptions={{
          requestNonPersonalizedAdsOnly: true,
        }}
      />}
      <Dialog.Container visible={isShowDialog}>
        <Dialog.Title>Contact</Dialog.Title>
        <Dialog.Description>
          <Text style={{lineHeight: 25}}>Please contact us via email </Text>
          <View style={{justifyContent: 'center', alignItems: 'center', flexDirection: 'row'}}>
            <Text onPress={() => Linking.openURL('mailto:<EMAIL>')}
                  style={{
                    textDecorationLine: 'underline',
                    color: 'blue'
                  }}><EMAIL></Text>

            <TouchableOpacity style={{marginStart: 5}}
                              onPress={copyEmail}>
              <MaterialCommunityIcons name="content-copy" size={20} color="black" />
            </TouchableOpacity>
          </View>
        </Dialog.Description>
        <Dialog.Button label="OK" onPress={handleCancel} />
      </Dialog.Container>
    </View>
  );
}

export default SettingScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1
  },
  accountContainer: {
    flexDirection: 'row',
    marginTop: 15,
    alignItems: 'flex-end',
    marginHorizontal: 15,
    marginBottom: 49,
    display: 'none'
  },
  avatarContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50
  },
  camera: {
    position: 'absolute',
    left: 68
  }
});
