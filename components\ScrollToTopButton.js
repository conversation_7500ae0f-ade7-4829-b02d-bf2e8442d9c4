import {FontAwesome5} from '@expo/vector-icons';
import {Pressable, StyleSheet} from 'react-native';
import * as React from 'react';
import {Colors} from '../constants/styles';
import {Component} from 'react';

export default class ScrollToTopButton extends Component {
    constructor(props) {
        super(props)
        this.state = {
            display: props.initialDisplay
        }
    }

    changeDisplay(value) {
        this.setState({display: value});
    }

    render() {
        const {display} = this.state;
        return (
            <Pressable style={[styles.scrollToTop, {display: display ? null : 'none'}]}
                       onPress={this.props.onPress}>
                <FontAwesome5 name="arrow-up" size={20} color="white" />
            </Pressable>
        )
    }
}

const styles = StyleSheet.create({
    scrollToTop: {
        width: 36,
        height: 36,
        position: 'absolute',
        bottom: 10,
        right: 10,
        borderRadius: 18,
        backgroundColor: Colors.primary100,
        justifyContent: 'center',
        alignItems: 'center'
    }
});
