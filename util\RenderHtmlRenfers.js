import {defaultHTMLElementModels, HTMLContentModel} from 'react-native-render-html';
import Player from '../components/Player';
import WebView from 'react-native-webview';
import TableRenderer, {cssRulesFromSpecs, tableModel} from '@native-html/table-plugin';
import ClickableImage from '../components/ClickableImage';
import YoutubPlayer from "../components/YoutubPlayer";

export const customHTMLElementModels = {
    audio: defaultHTMLElementModels.audio.extend({
        contentModel: HTMLContentModel.block,
    }),
    embed: defaultHTMLElementModels.embed.extend({
        contentModel: HTMLContentModel.block,
    }),
    video: defaultHTMLElementModels.video.extend({
        contentModel: HTMLContentModel.block,
    }),
    table: tableModel
};

export const renderers = {
    embed: EmbedRenderer,
    video: EmbedRenderer,
    audio: AudioRenderer,
    img: ImageRenderer,
    table: TableRenderer
};

export const webViewProps = {
    androidLayerType: 'none',
    style: {opacity: 0.99}
}

const cssRules =
    cssRulesFromSpecs({
        thOddBackground: '#005082',
        thOddColor: '#FFFFFF',
        trEvenBackground: '#FFFFFF',
        trOddBackground: '#F2F2F2',
        tdBorderColor: '#C9C9C9',
        thBorderColor: '#286A9F',
        outerBorderColor: '#C9C9C9',
        outerBorderWidthPx: 1,
        columnsBorderWidthPx: 0.7,
        selectableText: true
    }) +
    `
td {
padding: 5px;
text-align: justify;
}
`;

export const newHtmlProps = {
    WebView,
    renderers: renderers,
    renderersProps: {
        table: {
            cssRules,
            webViewProps
        }
    },
    customHTMLElementModels: customHTMLElementModels
};

function AudioRenderer({tnode}) {
    const audioSrc = tnode.domNode.attribs.src;

    return <Player audioUrl={audioSrc} audioOnly={true}/>
}

function EmbedRenderer({tnode}) {
    const videoSrc = tnode.domNode.attribs.src;

    const youtubeVideoId = extractVideoID(videoSrc);
    if (youtubeVideoId) {
        return <YoutubPlayer videoId={youtubeVideoId}/>
    }

    return <Player audioUrl={videoSrc} audioOnly={false}/>
}

function extractVideoID(url) {
    const regExp = /^.*((youtu.be\/)|(v\/)|(\/u\/\w\/)|(embed\/)|(watch\?))\??v?=?([^#&?]*).*/;
    const match = url.match(regExp);
    if (match && match[7].length === 11) {
        return match[7];
    } else {
        return "";
    }
}


function ImageRenderer(props) {
    return <ClickableImage props={props}/>
}
