import {
    findNodeHandle,
    NativeModules,
    Platform,
    ScrollView,
    StyleSheet,
    Text,
    useWindowDimensions,
    View
} from "react-native";
import {useContext, useEffect, useLayoutEffect, useRef, useState} from "react";
import RenderHtml from "react-native-render-html";
import {BANNER_ARTICLE_AD_UNIT_ID, SHOW_BANNER_AD} from "../constants/constants";
import {BannerAd, BannerAdSize} from "react-native-google-mobile-ads";
import {Colors} from "../constants/styles";
import IconButton from "../ui/IconButton";
import {MaterialCommunityIcons} from "@expo/vector-icons";
import {getPostInfo} from "../util/PostApi";
import {AuthContext} from "../store/AuthContext";
import * as React from 'react';
import analytics from "@react-native-firebase/analytics";
import {newHtmlProps} from '../util/RenderHtmlRenfers';
import AsyncStorage from "@react-native-async-storage/async-storage";
import ScrollToTopButton from "../components/ScrollToTopButton";

function ArticleDetailScreen({navigation, route}) {
    const [article, setArticle] = useState(null);
    const [articleIdx, setArticleIdx] = useState(0);
    const [loading, setLoading] = useState(false);

    const authCtx = useContext(AuthContext);
    const articleScrollViewRef = useRef(null);
    const scrollToTopButtonRef = useRef(null);
    let scrollIndex = 0;
    let lastSavePosition = 0;
    let scrollTimeout = null;

    const articleId = route.params.articleId;
    const categoryName = route.params.categoryName;
    const categoryId = route.params.categoryId;
    const articleIds = route.params.articleIds;

    const width = useWindowDimensions().width;
    const MemoizedRenderHtml = React.memo(RenderHtml);

    const tagsStyles = {
        body: {
            fontSize: 16,
            lineHeight: 27,
            textAlign: 'justify'
        },
        img: {
            maxWidth: width * 0.9,
            height: 'auto'
        },
        h1: {
            lineHeight: 33,
            marginVertical: 10
        },
        h2: {
            lineHeight: 31,
            marginVertical: 10
        },
        h3: {
            fontSize: 19,
            marginVertical: 10
        },
        h4: {
            fontSize: 18,
            marginVertical: 10
        },
        h5: {
            fontSize: 17,
            marginVertical: 10
        },
        h6: {
            fontSize: 16,
            marginVertical: 10
        },
        p: {
            marginVertical: 5
        },
        ol: {
            marginVertical: 5
        },
        iframe: {
            opacity: 0.99
        },
        // If you are using @native-html/table-plugin
        table: {
            opacity: 0.99
        }
    };

    useLayoutEffect(() => {
        navigation.setOptions({
            title: categoryName
        });
    }, [categoryName, navigation]);

    useEffect(() => {
        getArticle(articleId);

        articleIds.forEach((value, index) => {
            if (value === articleId) {
                setArticleIdx(index);
            }
        });
    }, []);

    function checkNoScroll(scrollLayoutHeight) {
        if (scrollTimeout) {
            clearTimeout(scrollTimeout);
        }
        scrollTimeout = setTimeout(() => {
            const ScrollViewManager = NativeModules.ScrollViewManager;
            if (ScrollViewManager
                && ScrollViewManager.getContentSize
                && articleScrollViewRef.current) {
                ScrollViewManager.getContentSize(findNodeHandle(articleScrollViewRef.current), (contentSize) => {
                    if (Math.ceil(scrollLayoutHeight) >= contentSize.height) {
                        handleScrollToEnd();
                    }
                })
            }
        }, 2000);
    }

    function logEvent(articleId, articleName) {
        analytics().logEvent('article_view', {
            id: '' + articleId,
            name: articleName,
            parent_id: '' + categoryId,
            parent_name: categoryName
        }).catch((error) => console.error(error));
    }

    async function getArticle(articleId) {
        try {
            setLoading(true);
            const articleData = await getPostInfo(authCtx, articleId);
            setArticle(articleData);

            AsyncStorage.setItem("bookmark_article_" + categoryId, articleId + "").catch(
                (error) => console.error(error));

            lastSavePosition = 0;
            await scrollToSavedPosition(articleId);

            await logEvent(articleId, articleData.title);
        } catch (ex) {
            console.error(ex);
        } finally {
            setLoading(false);
        }
    }

    async function slideArticle(position) {
        saveScrollPosition(scrollIndex, false);

        const nextIdx = articleIdx + position;
        if (position === 0 || nextIdx < 0 || nextIdx >= articleIds.length) {
            return;
        }

        await getArticle(articleIds[nextIdx]);
        setArticleIdx(nextIdx);
    }

    function handleScroll(event) {
        scrollIndex = event.nativeEvent.contentOffset.y;
        scrollToTopButtonRef.current?.changeDisplay(scrollIndex >= 200);
        saveScrollPosition(scrollIndex, true);
    }

    function saveScrollPosition(scrollIndex, isCheckTime) {
        const currentTimeInMilliseconds = Date.now();
        if (!isCheckTime || (currentTimeInMilliseconds > lastSavePosition + 1000)) {
            lastSavePosition = currentTimeInMilliseconds;
            AsyncStorage.setItem("lsp_article_" + articleIds[articleIdx], scrollIndex + "").catch(
                (error) => console.error(error));
        }
    }

    function isCloseToBottom({layoutMeasurement, contentOffset, contentSize}) {
        const paddingToBottom = 0;
        return Math.ceil(layoutMeasurement.height + contentOffset.y) >= contentSize.height - paddingToBottom;
    }

    async function handleScrollToEnd() {
        const articleId = articleIds[articleIdx];
        AsyncStorage.removeItem("lsp_article_" + articleId).catch(
            (error) => console.error(error));

        const finishData = await AsyncStorage.getItem('finished_articles_' + categoryId);
        let finishedArticles = !!finishData ?JSON.parse(finishData) : [];
        const itemSet = new Set(finishedArticles);
        if (!itemSet.has(articleId)) {
            finishedArticles = [...finishedArticles, articleId];
        }
        AsyncStorage.setItem("finished_articles_" + categoryId, JSON.stringify(finishedArticles)).catch(
            (error) => console.error(error));
    }

    async function scrollToSavedPosition(articleId) {
        let savedPosition = await AsyncStorage.getItem("lsp_article_" + articleId);
        savedPosition = !!savedPosition ? +savedPosition : 0;
        scrollTo(savedPosition);
    }

    function scrollTo(position) {
        articleScrollViewRef.current?.scrollTo({x: 0, y: position, animated: false});
        scrollToTopButtonRef.current?.changeDisplay(position >= 200);
    }

    if (!article) {
        return <View style={styles.noDataContainer}>
            <Text style={styles.noDataText}></Text>
        </View>
    }

    return (
        <>
            <View style={styles.container}>
                <View style={styles.titleOuterContainer}>
                    <View style={styles.titleInnerContainer}>
                        <Text style={styles.title}>{article.title}</Text>
                    </View>
                </View>

                <View style={{
                    borderColor: Colors.primary100,
                    borderWidth: 1,
                    paddingHorizontal: 5,
                    marginTop: 5,
                    backgroundColor: 'white',
                    flex: 1,
                    paddingVertical: 5
                }}>
                    <ScrollView
                        ref={articleScrollViewRef}
                        onScroll={handleScroll}
                        onMomentumScrollEnd={(event) => {
                            if (isCloseToBottom(event.nativeEvent)) {
                                handleScrollToEnd();
                            }
                        }}
                        onLayout={(event) => checkNoScroll(event.nativeEvent.layout.height)}
                        scrollEventThrottle={160}
                        overScrollMode={"never"}>
                        <MemoizedRenderHtml
                            contentWidth={width}
                            tagsStyles={tagsStyles}
                            source={{html: article.content}}
                            {...newHtmlProps}
                        />
                    </ScrollView>
                    <ScrollToTopButton
                        ref={scrollToTopButtonRef}
                        initialDisplay={false}
                        onPress={() => scrollTo(0)} />
                </View>

                <View>
                    <View style={styles.buttonGroupContainer}>
                        <View style={styles.buttonContainer}>
                            <View style={{flex: 1}}>
                                {articleIdx > 0 &&
                                    <IconButton style={styles.buttonSmall} textStyle={{fontSize: 14}}
                                                disable={loading}
                                                leftIcon={<MaterialCommunityIcons name="less-than" size={14}
                                                                                  color="white"/>}
                                                onPress={() => slideArticle(-1)}>Previous Article</IconButton>}
                            </View>

                            <View style={{flex: 1, alignItems: 'flex-end'}}>
                                {articleIdx < articleIds.length - 1 &&
                                    <IconButton style={styles.buttonSmall} textStyle={{fontSize: 14}}
                                                disable={loading}
                                                rightIcon={<MaterialCommunityIcons name="greater-than" size={14}
                                                                                   color="white"/>}
                                                onPress={() => slideArticle(1)}>Next Article</IconButton>}
                            </View>
                        </View>
                    </View>
                </View>
            </View>

            {SHOW_BANNER_AD &&
                <BannerAd
                    unitId={BANNER_ARTICLE_AD_UNIT_ID}
                    size={BannerAdSize.ANCHORED_ADAPTIVE_BANNER}
                    requestOptions={{
                        requestNonPersonalizedAdsOnly: true,
                    }}
                />}
        </>
    );
}

export default ArticleDetailScreen;

const styles = StyleSheet.create({
    noDataContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center'
    },
    container: {
        marginTop: 5,
        flex: 1,
        marginHorizontal: 15,
        justifyContent: 'space-between',
        flexGrow: 1,
        flexDirection: 'column'
    },
    titleOuterContainer: {
        minHeight: 57,
        backgroundColor: Colors.primary100,
        marginVertical: 5,
        borderRadius: 8,
        elevation: 2,
        borderColor: Colors.primary100,
        borderBottomWidth: 2,
        shadowColor: 'black',
        shadowOpacity: 0.15,
        shadowOffset: {width: 0, height: 2},
        shadowRadius: 8,
        overflow: Platform.select({ios: 'visible', android: 'hidden'})
    },
    titleInnerContainer: {
        minHeight: 55,
        backgroundColor: 'white',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 8,
        paddingHorizontal: 3,
        paddingVertical: 6,
        elevation: 2,
        shadowColor: 'black',
        shadowOpacity: 0.15,
        shadowOffset: {width: 0, height: 2},
        shadowRadius: 8,
        overflow: Platform.select({ios: 'visible', android: 'hidden'})
    },
    title: {
        color: Colors.accent100,
        fontWeight: 'bold',
        fontSize: 16,
        marginVertical: 10
    },
    buttonGroupContainer: {
        justifyContent: "center",
        alignItems: "center",
        flexGrow: 1,
        marginVertical: 5,
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center'
    },
    buttonSmall: {
        width: 140,
        height: 35,
        justifyContent: 'center',
        alignItems: 'center'
    },
    noDataText: {
        fontSize: 18
    }
});
