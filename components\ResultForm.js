import {ScrollView, StyleSheet, Text, View} from 'react-native';
import Button from '../ui/Button';
import {Colors} from '../constants/styles';
import {secondToTime} from '../util/Utils';

function ResultForm({
                        corrects,
                        totalQuestion,
                        answeredTime,
                        point,
                        totalPoint,
                        percentage,
                        passPercentage,
                        pass,
                        viewQuestionPress,
                        restartQuizPress,
                        categoryName,
                        resultText
                    }) {
    return (
        <View style={styles.container}>
            <ScrollView>
                <Text style={styles.resultText}>Results</Text>
                <Text style={styles.questionsText}>{corrects} of {totalQuestion} Questions answered correctly</Text>
                <Text style={styles.timeText}>Your time: {secondToTime(answeredTime)}</Text>
                <View style={styles.pointContainer}>
                    <Text style={styles.pointText}>You have reached {point} of {totalPoint} point(s),
                        ({percentage}%)</Text>
                </View>

                {!pass &&
                    <Text style={styles.questionsText}>
                        {resultText ? resultText
                            : 'Thank you for completing the ' + categoryName + ' Assessment. ' +
                            'We regret to inform you that you did not receive the minimum ' +
                            'passing score of ' + passPercentage + '% required to pass this assessment.'}
                    </Text>}

                {pass &&
                    <Text style={styles.questionsText}>
                        {resultText ? resultText : 'Congratulation! You pass the exam!'}
                    </Text>}

                <View style={styles.divideLine}></View>
                <View style={styles.buttonContainer}>
                    <Button textStyle={{fontSize: 14}}
                            style={[styles.button, {marginRight: 30}]}
                            onPress={viewQuestionPress}>Show Results</Button>
                    <Button textStyle={{fontSize: 14}}
                            style={styles.button}
                            onPress={restartQuizPress}>Restart Quiz</Button>
                </View>
            </ScrollView>
        </View>
    );
}

export default ResultForm;

const styles = StyleSheet.create({
    container: {
        marginTop: 10,
        flex: 1
    },
    resultText: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 10
    },
    questionsText: {
        fontSize: 16,
        marginBottom: 15,
        lineHeight: 25
    },
    timeText: {
        fontSize: 15,
        color: Colors.accent100,
        fontWeight: 'bold'
    },
    pointContainer: {
        marginVertical: 20,
        paddingVertical: 20,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: Colors.accent250,
        borderWidth: 2,
        borderColor: Colors.accent200,
        borderRadius: 8
    },
    pointText: {
        fontSize: 18,
        color: Colors.accent100,
        fontWeight: 'bold'
    },
    divideLine: {
        borderWidth: 1,
        borderColor: Colors.accent250
    },
    buttonContainer: {
        flexDirection: 'row',
        marginVertical: 15,
        marginHorizontal: 15,
        justifyContent: 'center',
        alignItems: 'center'
    },
    button: {
        height: 50,
        justifyContent: 'center',
        alignItems: 'center'
    },
})
