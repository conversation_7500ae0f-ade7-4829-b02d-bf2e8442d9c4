import React, {useState} from 'react';

import {View, Text, StyleSheet} from 'react-native';
import {Colors} from "../constants/styles";
import Slider from '@react-native-community/slider';

function SeekBar({trackLength, currentPosition, onSeek, onSlidingStart}) {
    const minutesAndSeconds = (position) => ([
        pad(Math.floor(position / 60), 2),
        pad(position % 60, 2),
    ]);
    const elapsed = minutesAndSeconds(currentPosition);
    const remaining = minutesAndSeconds(trackLength - currentPosition);

    function pad(n, width, z = 0) {
        n = n + '';
        return n.length >= width ? n : new Array(width - n.length + 1).join(z) + n;
    }

    return (
        <View style={styles.container}>
            <View style={{flexDirection: 'row'}}>
                <Text style={styles.text}>
                    {elapsed[0] + ":" + elapsed[1]}
                </Text>
                <View style={{flex: 1}}/>
                <Text style={[styles.text, {alignItems: "flex-end"}]}>
                    {trackLength > 1 && "-" + remaining[0] + ":" + remaining[1]}
                </Text>
            </View>
            <Slider
                maximumValue={Math.max(trackLength, 1, currentPosition + 1)}
                onSlidingStart={onSlidingStart}
                onSlidingComplete={onSeek}
                value={currentPosition}
                minimumTrackTintColor={Colors.primary100}
                maximumTrackTintColor={Colors.accent100}
            />
        </View>
    );
}

export default SeekBar;

const styles = StyleSheet.create({
    container: {
        paddingLeft: 16,
        paddingRight: 16,
        paddingTop: 10,
    },
    text: {
        color: Colors.accent100,
        fontSize: 12,
        fontWeight: 'bold',
        textAlign: 'center'
    }
});
