import React from 'react';
import {useWindowDimensions} from 'react-native';
import YoutubePlayer from 'react-native-youtube-iframe';

export default function YoutubPlayer({videoId}) {
    const width = useWindowDimensions().width;
    const height = Math.round(250 * width / 480);

    return (
        <YoutubePlayer
            height={height}
            play={false}
            videoId={videoId}
        />
    );
}
