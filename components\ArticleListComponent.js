import React, {memo} from "react";
import ArticleGridTile from "./ArticleGridTile";
import {useNavigation} from "@react-navigation/native";

const ArticleListComponent = ({itemData, categoryName, categoryId, articleIds}) => {
    const navigation = useNavigation();

    function pressHandler(id) {
        navigation.navigate("ArticleDetail", {
            articleId: id,
            categoryName: categoryName,
            categoryId: categoryId,
            articleIds: articleIds,
        });
    }

    return <ArticleGridTile
        title={itemData.item.title}
        isFinished={itemData.item.finished}
        isBookmarked={itemData.item.bookmarked}
        onPress={() => pressHandler(itemData.item.id)}/>;
}

export default memo(ArticleListComponent);
