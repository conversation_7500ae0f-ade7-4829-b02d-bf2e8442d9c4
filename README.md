# Ezami - Educational Exam App

Ezami is a comprehensive mobile educational platform built with React Native and Expo that provides interactive quiz and exam functionality. The app offers a complete learning experience with practice tests, full exams, and educational articles to help users prepare for various career-focused assessments.

## 🚀 Features

### Core Functionality
- **Interactive Quiz System**: Take practice tests and full exams with multiple choice questions
- **Test Categories**: Organized exam categories for different subjects/careers
- **Mini & Full Tests**: Choose between quick practice sessions (Mini) or comprehensive exams (Full)
- **Real-time Progress Tracking**: Track your performance, scores, and completion status
- **Resume Capability**: Save and resume incomplete tests
- **Detailed Results**: Get comprehensive feedback with explanations and scoring

### Educational Content
- **Article System**: Access educational articles organized by categories
- **Rich Content Support**: HTML content rendering with images, tables, and multimedia
- **Educational Videos**: Integrated YouTube video player for learning materials

### User Experience
- **User Authentication**: Secure login/register system with password management
- **Onboarding Experience**: Interactive app introduction for new users
- **Offline Capability**: Save progress locally and sync when online
- **Multi-language Support**: Built-in internationalization support
- **Dark/Light Theme**: Responsive UI design

### Premium Features
- **In-App Purchases**: Premium subscriptions for full access to content
- **Ad Integration**: Google Mobile Ads with premium ad-free experience
- **Purchase Management**: RevenueCat integration for subscription management

### Technical Features
- **Cross-platform**: iOS and Android support
- **Firebase Integration**: Analytics and crash reporting
- **Secure Storage**: Encrypted local data storage
- **Performance Optimized**: Fast loading and smooth animations

## 📱 Screenshots

The app includes various screens for different functionalities:
- Authentication (Login/Register)
- Category browsing
- Test taking interface
- Results and progress tracking
- Settings and account management
- Article reading
- Paywall and subscription management

## 🛠 Technology Stack

### Frontend
- **React Native** (0.72.10) - Cross-platform mobile development
- **Expo** (49.0.23) - Development platform and build tools
- **React Navigation** - Navigation system with drawer, stack, and tab navigators
- **React Native Reanimated** - Advanced animations and gestures

### Backend Integration
- **Axios** - HTTP client for API communication
- **JWT** - Authentication token management
- **REST API** - Backend communication via RESTful services

### Storage & Data
- **AsyncStorage** - Local data persistence
- **React Native Keychain** - Secure credential storage
- **Context API** - State management for app, auth, and quiz data

### Media & Content
- **React Native Video** - Video playback functionality
- **YouTube iframe** - YouTube video integration
- **React Native Render HTML** - Rich HTML content rendering
- **React Native SVG** - Vector graphics support
- **Fast Image** - Optimized image loading

### Monetization & Analytics
- **Google Mobile Ads** - Advertisement integration
- **RevenueCat** - Subscription and purchase management
- **Firebase Analytics** - User behavior tracking
- **Firebase Crashlytics** - Crash reporting and monitoring

### UI/UX Components
- **React Native Elements** - UI component library
- **Vector Icons** - Icon sets (@expo/vector-icons)
- **Modal DateTime Picker** - Date/time selection
- **Progress Indicators** - Visual progress feedback
- **Toast Messages** - User notifications

### Development Tools
- **Expo Dev Client** - Custom development builds
- **Babel** - JavaScript transpilation
- **Patch Package** - NPM package modifications

## 📋 Prerequisites

Before running this project, make sure you have:

- **Node.js** (version 14 or higher)
- **npm** or **yarn** package manager
- **Expo CLI** (`npm install -g @expo/cli`)
- **iOS Simulator** (for iOS development) or **Xcode**
- **Android Studio** and **Android SDK** (for Android development)
- **Git** for version control

### For Physical Device Testing
- **Expo Go app** installed on your mobile device
- Both your computer and mobile device connected to the same Wi-Fi network

## 🚀 Getting Started

### 1. Clone the Repository
```bash
git clone <repository-url>
cd ezami
```

### 2. Install Dependencies
```bash
npm install
# or
yarn install
```

### 3. Set Up Environment
Create environment configuration files if needed and configure:
- Firebase project settings
- Google AdMob ad unit IDs
- RevenueCat API keys
- Backend API endpoints

### 4. Run the Application

#### Development with Expo Go
```bash
npm start
# or
expo start --dev-client
```

#### Platform-specific builds
```bash
# iOS
npm run ios
# or
expo run:ios

# Android
npm run android
# or
expo run:android

# Web
npm run web
# or
expo start --web
```

### 5. Development Setup

The app uses Expo managed workflow with custom dev client for native dependencies.

## 📁 Project Structure

```
├── components/          # Reusable UI components
├── screens/            # Application screens/pages
├── store/              # Context providers for state management
├── util/               # Utility functions and API calls
├── models/             # Data models and types
├── constants/          # App constants and configuration
├── ui/                 # Basic UI building blocks
├── assets/             # Static assets (images, icons)
├── android/            # Android-specific configuration
├── ios/                # iOS-specific configuration
└── patches/            # Package modifications
```

## 🔧 Configuration

### Environment Variables
The app uses environment-based configuration for:
- API endpoints (`BE_URL`)
- Ad unit IDs (development vs production)
- Feature flags
- Third-party service keys

### Revenue Cat Setup
Configure RevenueCat for subscription management:
1. Create RevenueCat account
2. Set up products and offerings
3. Configure API keys in constants

## 🧪 Testing

The project includes test configurations for:
- Unit testing with Jest
- Integration testing
- End-to-end testing capabilities

Run tests:
```bash
npm test
```

## 📱 Building for Production

### EAS Build (Recommended)
```bash
# Install EAS CLI
npm install -g eas-cli

# Configure EAS
eas build:configure

# Build for iOS
eas build --platform ios

# Build for Android
eas build --platform android --profile production
```

### Local Builds
Follow Expo documentation for local builds with EAS Build or configure manual build processes.

## 🔒 Security Features

- Secure token storage using React Native Keychain
- JWT-based authentication
- Encrypted local data storage
- HTTPS API communication
- Input validation and sanitization

## 📈 Performance Optimizations

- Lazy loading of screens and components
- Image optimization with Fast Image
- Efficient list rendering with FlatList
- Background task management
- Memory management for large datasets

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is proprietary software. All rights reserved.

## 📞 Support

For support and inquiries, please contact the development team or visit the official website.

## 🔄 Version History

- **v1.5.6** - Current version with latest features and improvements
- Regular updates and feature enhancements
- Bug fixes and performance improvements

---

**Built with ❤️ using React Native and Expo**
