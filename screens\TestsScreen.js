import {FlatList, Platform, StyleSheet, Text, View} from 'react-native';
import {useContext, useEffect, useLayoutEffect, useState} from 'react';
import TestGridTile from '../components/TestGridTile';
import {AuthContext} from '../store/AuthContext';
import {getQuizList} from '../util/QuizApi';
import {BannerAd, BannerAdSize, TestIds} from 'react-native-google-mobile-ads';
import {BANNER_AD_UNIT_ID, SHOW_BANNER_AD} from '../constants/constants';
import {useIsFocused} from "@react-navigation/native";
import LoadingOverlay from "../ui/LoadingOverlay";

function TestsScreen({navigation, route}) {
  const [displayedTest, setDisplayedTest] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const catTitle = route.params.categoryTitle;
  const catHeader = route.params.categoryHeader;
  const typeTestTitle = route.params.typeTestTitle;
  const typeTestCode = route.params.typeTestCode;
  const isPurchased = route.params.isPurchased;

  const authCtx = useContext(AuthContext);
  const isFocused = useIsFocused();

  async function getQuizData() {
    try {
      setIsLoading(true);
      const quizData = await getQuizList(authCtx, catTitle, typeTestCode);
      setDisplayedTest(quizData);
    } catch (e) {
      console.error(e);
    } finally {
      setIsLoading(false);
    }
  }

  useEffect(() => {
    if (isFocused) {
      getQuizData();
    }
  }, [isFocused])

  useLayoutEffect(() => {
    navigation.setOptions({
      title: (catHeader ? catHeader : catTitle) + ': ' + typeTestTitle
    });
  }, [catHeader, catTitle, typeTestTitle, navigation]);

  function renderTestItem(itemData) {
    function pressHandler() {
      navigation.navigate("TestDetail", {
        testId: itemData.item.id,
        categoryTitle: catTitle,
        categoryHeader: catHeader,
        typeTestTitle: typeTestTitle,
        typeTestCode: typeTestCode,
        isPurchased: isPurchased
      });
    }

    return <TestGridTile
      index={itemData.index}
      item={itemData.item}
      onPress={pressHandler} />;
  }

  if (isLoading) {
    return <LoadingOverlay message={'Loading...'} />;
  }

  if (displayedTest.length === 0) {
    return <View style={styles.container}>
      <Text style={styles.noDataText}>No Data</Text>
    </View>
  }

  return (
    <View style={{flex: 1}}>
      <FlatList style={{paddingHorizontal: 14, marginTop: 10}}
                data={displayedTest}
                keyExtractor={(item) => item.id}
                renderItem={renderTestItem}
                numColumns={1} />

      {SHOW_BANNER_AD && !isPurchased &&
       <BannerAd
         unitId={BANNER_AD_UNIT_ID}
         size={BannerAdSize.ANCHORED_ADAPTIVE_BANNER}
         requestOptions={{
           requestNonPersonalizedAdsOnly: true,
         }}
       />}
    </View>
  );
}

export default TestsScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center'
  },
  noDataText: {
    fontSize: 18
  }
})
