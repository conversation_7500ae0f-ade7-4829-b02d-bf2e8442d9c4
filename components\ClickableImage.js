import {View} from 'react-native';
import React, {useState} from 'react';
import ImageView from "react-native-image-viewing";
import {useInternalRenderer} from 'react-native-render-html';

export default function ClickableImage({props}) {
    const [visible, setIsVisible] = useState(false);
    const {Renderer, rendererProps} = useInternalRenderer('img', props);
    const imageUri = rendererProps.source.uri;
    const thumbnailSource = {
        ...rendererProps.source,
        // You could change the uri here, for example to provide a thumbnail.
        uri: imageUri
    };

    function handleImagePress() {
        setIsVisible(true);
    }

    return (
        <View>
            <Renderer {...rendererProps} source={thumbnailSource} onPress={handleImagePress} />
            <ImageView
                images={[{uri: imageUri}]}
                imageIndex={0}
                visible={visible}
                onRequestClose={() => setIsVisible(false)}
            />
        </View>
    )
}
