import {StyleSheet, Text, View} from 'react-native';
import {Colors} from '../constants/styles';
import GridTile from './GridTile';
import {MaterialCommunityIcons} from "@expo/vector-icons";

function CategoryDetailGridTile({title, numQuiz, onPress, style, isMini, remainDay}) {
  const description = numQuiz + (numQuiz <= 1 ? ' Exam' : ' Exams');
  return (
    <GridTile onPress={onPress} style={{height: 160, ...style}}>
      <View style={styles.title}>
        <Text style={styles.text}>{title}</Text>
        <Text style={styles.description}>{description}</Text>
      </View>

      {isMini &&
       <View style={{alignItems: 'center', justifyContent: 'center', flex: 1}}>
         <Text style={{fontWeight: "bold"}}>FREE Access</Text>
       </View>
      }

      {!isMini && (remainDay > 0 || isNaN(remainDay)) &&
       <View style={{alignItems: 'center', justifyContent: 'center', flex: 1}}>
         <Text style={{fontWeight: "bold"}}>Full Access</Text>
         {!isNaN(remainDay) &&
          <Text><Text style={{color: Colors.primary100}}>{remainDay}</Text> days remain</Text>}
       </View>
      }

      {
        !isMini && remainDay <= 0 &&
        <View style={{alignItems: 'center', justifyContent: 'center', flex: 1}}>
          <MaterialCommunityIcons name="lock-outline" size={24} color={Colors.primary100} />
          <Text style={{color: Colors.primary100, textAlign: 'center'}}>Purchase to unlock the full test</Text>
        </View>}
    </GridTile>
  );
}

export default CategoryDetailGridTile;

const styles = StyleSheet.create({
  text: {
    fontWeight: 'bold',
    fontSize: 18,
    color: Colors.accent100,
  },
  description: {
    fontSize: 13,
    fontWeight: "300"
  },
  title: {
    marginTop: 10,
    alignItems: 'center'
  }
});
