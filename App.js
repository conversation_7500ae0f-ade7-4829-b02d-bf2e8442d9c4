import 'react-native-gesture-handler';
import LoginScreen from './screens/LoginScreen';
import RegisterScreen from './screens/RegisterScreen';
import {NavigationContainer} from '@react-navigation/native';
import {useCallback, useContext, useEffect, useRef, useState} from 'react';
import AuthProvider, {AuthContext} from './store/AuthContext';
import {createDrawerNavigator} from '@react-navigation/drawer';
import CategoryScreen from './screens/CategoryScreen';
import SettingScreen from './screens/SettingScreen';
import {StatusBar} from 'expo-status-bar';
import {Colors} from './constants/styles';
import {Platform, Pressable} from 'react-native';
import {Feather} from '@expo/vector-icons';
import IconRoundButton from './ui/IconRoundButton';
import CategoryDetailScreen from './screens/CategoryDetailScreen';
import TestsScreen from './screens/TestsScreen';
import TestDetailScreen from './screens/TestDetailScreen';
import AccountSettingScreen from './screens/AccountSettingScreen';
import ChangePasswordScreen from './screens/ChangePasswordScreen';
import SoftwareInfoScreen from './screens/SoftwareInfoScreen';
import ContactScreen from './screens/ContactScreen';
import LoadingOverlay from './ui/LoadingOverlay';
import mobileAds, {MaxAdContentRating} from 'react-native-google-mobile-ads';
import {requestTrackingPermissionsAsync} from 'expo-tracking-transparency';
import Purchases, {LOG_LEVEL} from "react-native-purchases";
import {ENABLE_REVENUE_CAT, REVENUE_CAT_API_KEYS} from "./constants/constants";
import PaywallScreen from "./screens/PaywallScreen";
import {createStackNavigator} from "@react-navigation/stack";
import AppProvider, {AppContext} from "./store/AppContext";
import OnboardingScreen from "./screens/OnboardingScreen";
import * as SplashScreen from "expo-splash-screen";
import Toast from "react-native-toast-message";
import {createMaterialTopTabNavigator} from "@react-navigation/material-top-tabs";
import ArticleCategoryScreen from "./screens/ArticleCategoryScreen";
import ArticlesScreen from "./screens/ArticlesScreen";
import ArticleDetailScreen from "./screens/ArticleDetailScreen";
import ForgetPasswordScreen from "./screens/ForgetPasswordScreen";
import analytics from "@react-native-firebase/analytics";
import crashlytics from '@react-native-firebase/crashlytics';
import QuizProvider from "./store/QuizContext";

const Stack = createStackNavigator();
const Drawer = createDrawerNavigator();
const Tab = createMaterialTopTabNavigator();
// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync();

function AuthStack({initialRouteName}) {
    return (
        <Stack.Navigator
            initialRouteName={initialRouteName ? initialRouteName : 'Login'}
            screenOptions={{
                contentStyle: {
                    backgroundColor: Colors.background,
                },
                headerStyle: {
                    backgroundColor: Colors.primary100,
                },
                headerTintColor: 'white',
                headerBackTitleVisible: false
            }}>
            <Stack.Screen options={{headerShown: false}} name='Onboarding' component={OnboardingScreen}/>
            <Stack.Screen name='Login' component={LoginScreen}/>
            <Stack.Screen name='Register' component={RegisterScreen}/>
            <Stack.Screen name='ForgetPassword' component={ForgetPasswordScreen} options={{
                title: 'Reset Password'
            }}/>
        </Stack.Navigator>
    );
}

function AuthenticatedStack() {
    return (
        <Stack.Navigator screenOptions={{
            contentStyle: {
                backgroundColor: Colors.background,
            },
            headerStyle: {
                backgroundColor: Colors.primary100,
            },
            headerTintColor: 'white',
            headerBackTitleVisible: false
        }}>
            <Stack.Screen name='Drawer' component={AuthenticatedDrawer} options={{
                headerShown: false
            }}/>
            <Stack.Screen name='CategoryDetail' component={CategoryDetailScreen}/>
            <Stack.Screen name='Tests' component={TestsScreen}/>
            <Stack.Screen name='TestDetail' component={TestDetailScreen}/>
            <Stack.Screen name='Paywall' component={PaywallScreen}/>
            <Stack.Screen name='Articles' component={ArticlesScreen}/>
            <Stack.Screen name='ArticleDetail' options={{
                animationEnabled: Platform.select({
                    ios: true,
                    android: false,
                })
            }} component={ArticleDetailScreen}/>
            <Stack.Screen name='AccountSetting' component={AccountSettingScreen} options={{
                title: 'Account'
            }}/>
            <Stack.Screen name='ChangePass' component={ChangePasswordScreen} options={{
                title: 'Change password'
            }}/>
            <Stack.Screen name='SoftwareInfo' component={SoftwareInfoScreen} options={{
                title: 'Software Information'
            }}/>
            <Stack.Screen name='ContactUs' component={ContactScreen} options={{
                title: 'Contact'
            }}/>
        </Stack.Navigator>
    );
}

function AuthenticatedDrawer() {
    return (
        <Drawer.Navigator screenOptions={({navigation}) => ({
            headerLeft: props => <Pressable style={{marginLeft: 10}} onPress={navigation.toggleDrawer}>
                <Feather name='menu' size={24} color='white'/>
            </Pressable>,
            headerStyle: {
                backgroundColor: Colors.primary100,
            },
            headerTintColor: 'white',
            drawerInactiveTintColor: Colors.primary100,
            drawerActiveTintColor: Colors.accent100,
            drawerActiveBackgroundColor: Colors.error100,
        })}>
            <Drawer.Screen name='Home' component={TabScreen}
                           options={{
                               title: 'Home',
                               headerTitle: '',
                               headerRight: props => <IconRoundButton icon='bell-badge-outline'
                                                                      color={Colors.primary100}
                                                                      size={24}/>,
                               drawerIcon: ({color, size}) => <Feather name='book' color={color} size={size}/>
                           }}/>
            <Drawer.Screen name='Setting' component={SettingScreen}
                           options={{
                               title: 'Setting',
                               drawerIcon: ({color, size}) => <Feather name='settings' color={color} size={size}/>
                           }}/>
        </Drawer.Navigator>
    );
}

function TabScreen() {
    return (
        <Tab.Navigator>
            <Tab.Screen name='Exams Preparation' component={CategoryScreen}/>
            <Tab.Screen name='Books & Guides' component={ArticleCategoryScreen}/>
        </Tab.Navigator>
    );
}

function Navigation() {
    const authCtx = useContext(AuthContext);
    const appCtx = useContext(AppContext);
    const isAuthenticated = authCtx.isAuthenticated;
    const isFirstLaunch = appCtx.isFirstLaunch;
    const [appIsReady, setAppIsReady] = useState(false);

    const routeNameRef = useRef();
    const navigationRef = useRef();

    useEffect(() => {
        async function prepare() {
            try {
                await authCtx.loadToken();
                await appCtx.loadOnboardingState();
            } catch (e) {
                console.warn(e);
            } finally {
                // Tell the application to render
                setAppIsReady(true);
            }
        }

        prepare();
    }, []);

    const onLayoutRootView = useCallback(async () => {
        if (appIsReady) {
            // This tells the splash screen to hide immediately! If we call this after
            // `setAppIsReady`, then we may see a blank screen while the app is
            // loading its initial state and rendering its first pixels. So instead,
            // we hide the splash screen once we know the root view has already
            // performed layout.
            await SplashScreen.hideAsync();
        }
        routeNameRef.current = navigationRef.current.getCurrentRoute().name;
    }, [appIsReady]);

    if (authCtx.isLoading) {
        return <LoadingOverlay message={'Loading data...'}/>;
    }

    if (!appIsReady) {
        return null;
    }

    return (
        <NavigationContainer
            ref={navigationRef}
            onReady={onLayoutRootView}
            onStateChange={async () => {
                const previousRouteName = routeNameRef.current;
                const currentRouteName = navigationRef.current.getCurrentRoute().name;

                if (previousRouteName !== currentRouteName) {
                    await analytics().logScreenView({
                        screen_name: currentRouteName,
                        screen_class: currentRouteName,
                    });
                }
                routeNameRef.current = currentRouteName;
            }}
        >
            {!isAuthenticated && <AuthStack initialRouteName={isFirstLaunch ? 'Onboarding' : 'Login'}/>}
            {isAuthenticated && <AuthenticatedStack/>}
            <Toast/>
        </NavigationContainer>
    );
}

export default function App() {

    function initAd() {
        mobileAds().setRequestConfiguration({
            // Update all future requests suitable for parental guidance
            maxAdContentRating: MaxAdContentRating.G,

            // Indicates that you want your content treated as child-directed for purposes of COPPA.
            tagForChildDirectedTreatment: false,

            // Indicates that you want the ad request to be handled in a
            // manner suitable for users under the age of consent.
            tagForUnderAgeOfConsent: false,

            // An array of test device IDs to allow.
            testDeviceIdentifiers: ['EMULATOR', 'bffb271d2b35d4b9f92606916a85c00e'],
        }).then(() => {
            // Request config successfully set!
            mobileAds().initialize().then(adapterStatuses => {
                // Initialization complete!
                console.log("adapterStatuses: " + adapterStatuses);
            });
        });
    }

    async function initRevenueCat() {
        if (!ENABLE_REVENUE_CAT) {
            return;
        }
        await Purchases.setLogLevel(LOG_LEVEL.DEBUG);

        if (Platform.OS === 'ios') {
            await Purchases.configure({apiKey: REVENUE_CAT_API_KEYS.apple});
        } else if (Platform.OS === 'android') {
            await Purchases.configure({apiKey: REVENUE_CAT_API_KEYS.google});
        }
    }

    async function requestConsent() {
        const {status} = await requestTrackingPermissionsAsync();
        if (status === 'granted') {
            console.log('Yay! I have user permission to track data');
        }
        await analytics().setAnalyticsCollectionEnabled(true);
        await crashlytics().setCrashlyticsCollectionEnabled(true);
    }

    useEffect(() => {
        initAd();

        initRevenueCat();

        setTimeout(async () => {
            await requestConsent();
        }, 500);
    }, [])

    return (
        <>
            <StatusBar style="light"/>

            <AuthProvider>
                <AppProvider>
                    <QuizProvider>
                        <Navigation/>
                    </QuizProvider>
                </AppProvider>
            </AuthProvider>
        </>
    );
}
