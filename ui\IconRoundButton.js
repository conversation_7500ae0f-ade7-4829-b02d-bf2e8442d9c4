import {Pressable, StyleSheet} from 'react-native';
import {MaterialCommunityIcons} from '@expo/vector-icons';
import {Colors} from '../constants/styles';

function IconRoundButton({icon, color, size, noBackground, onPress, width, height, margin}) {
  const btnStyle = {
    width: width ? width : 35,
    height: height ? height : 35,
    margin: margin ? margin : 12
  }
  return (
    <Pressable
      style={({pressed}) => [styles.button, btnStyle, pressed && styles.pressed, !noBackground && styles.background]}
      onPress={onPress}
    >
      <MaterialCommunityIcons name={icon} color={color} size={size} />
    </Pressable>
  );
}

export default IconRoundButton;

const styles = StyleSheet.create({
  button: {
    margin: 12,
    borderRadius: 17,
    alignItems: 'center',
    justifyContent: 'center'
  },
  background: {
    backgroundColor: Colors.accent200
  },
  pressed: {
    opacity: 0.7,
  },
});
