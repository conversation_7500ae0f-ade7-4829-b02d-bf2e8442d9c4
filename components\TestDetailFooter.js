import {StyleSheet, View, Text} from 'react-native';
import IconButton from '../ui/IconButton';
import {MaterialCommunityIcons} from '@expo/vector-icons';
import Button from '../ui/Button';

function TestDetailFooter({currQuestionIdx, totalQuestions, backPress, nextPress, finishPress, isFinish}) {
  return (
    <View style={styles.container}>
      <View style={styles.buttonContainer}>
        <View style={{flex: 1}}>
          {currQuestionIdx > 0 &&
           <IconButton style={styles.buttonSmall} textStyle={{fontSize: 14}}
                       leftIcon={<MaterialCommunityIcons name="less-than" size={14} color="white" />}
                       onPress={backPress}>Previous</IconButton>}
        </View>

        <View style={{flex: 1, alignItems: 'flex-end'}}>
          {currQuestionIdx < totalQuestions - 1 &&
           <IconButton style={styles.buttonSmall} textStyle={{fontSize: 14}}
                       rightIcon={<MaterialCommunityIcons name="greater-than" size={14} color="white" />}
                       onPress={nextPress}>Next</IconButton>}
        </View>
      </View>

      <View style={styles.buttonContainer}>
        {!isFinish && currQuestionIdx >= totalQuestions - 1 &&
         <Button style={styles.button} onPress={finishPress}>Finish now</Button>}
      </View>
    </View>
  );
}

export default TestDetailFooter;

const styles = StyleSheet.create({
  container: {
    justifyContent: "center",
    alignItems: "center",
    flexGrow: 1,
    marginVertical: 5,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center'
  },
  button: {
    width: 237,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 10
  },
  buttonSmall: {
    width: 90,
    height: 35,
    justifyContent: 'center',
    alignItems: 'center'
  }
});
