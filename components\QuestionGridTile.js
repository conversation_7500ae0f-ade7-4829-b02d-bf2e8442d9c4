import {Platform, Pressable, StyleSheet, Text, View} from 'react-native';
import {Colors} from '../constants/styles';

function QuestionGridTile({title, isCurrent, answered, reviewed, onPress, isFinish, correct}) {
  let moreStyle = {
    borderWidth: isCurrent ? 1 : 0,
    backgroundColor: answered ? Colors.accent200 : (reviewed ? Colors.primary600 : 'white'),
  };

  if (isFinish) {
    moreStyle = {
      borderWidth: isCurrent ? 1 : 0,
      backgroundColor: correct ? Colors.primary900 : Colors.error500
    }
  }

  return (
    <View style={[styles.gridItem, moreStyle]}>
      <Pressable android_ripple={{color: Colors.accent200}}
                 style={({pressed}) => [styles.button, pressed ? styles.buttonPressed : null]}
                 onPress={onPress}>
        <View style={styles.innerContainer}>
          <Text>{title}</Text>
        </View>
      </Pressable>
    </View>
  );
}

export default QuestionGridTile;

const styles = StyleSheet.create({
  gridItem: {
    width: 30,
    height: 30,
    marginHorizontal: 2,
    marginTop: 5,
    borderRadius: 8,
    backgroundColor: 'white',
    borderWidth: 0,
    borderColor: Colors.primary800,
    // shadowColor: 'black',
    // shadowOpacity: 0.25,
    // shadowOffset: {width: 0, height: 2},
    // shadowRadius: 8,
    overflow: Platform.select({ios: 'visible', android: 'hidden'})
  },
  button: {
    flex: 1
  },
  buttonPressed: {
    opacity: 0.5
  },
  innerContainer: {
    flex: 1,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center'
  }
});
