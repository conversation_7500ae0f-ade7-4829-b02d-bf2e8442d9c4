import {<PERSON>ert, Image, ScrollView, StyleSheet, View} from 'react-native';
import {Feather} from '@expo/vector-icons';
import {Colors} from '../constants/styles';
import Input from '../ui/Input';
import {useContext, useEffect, useState} from 'react';
import Button from '../ui/Button';
import LoadingOverlay from '../ui/LoadingOverlay';
import {getUserInfo, updateUserInfo} from '../util/UserApi';
import {AuthContext} from '../store/AuthContext';
import {BANNER_AD_UNIT_ID, SHOW_BANNER_AD} from '../constants/constants';
import {BannerAd, BannerAdSize} from 'react-native-google-mobile-ads';

function AccountSettingScreen() {
    const [dob, setDob] = useState(null);
    const [fullName, setFullName] = useState(null);
    const [email, setEmail] = useState(null);
    const [mobile, setMobile] = useState(null);
    const [isAuthenticating, setIsAuthenticating] = useState(false);
    const [loadingMess, setLoadingMess] = useState("");
    const authCtx = useContext(AuthContext);

    useEffect(() => {
        async function getData() {
            try {
                setIsAuthenticating(true);
                setLoadingMess("");
                const userInfo = await getUserInfo(authCtx.token);
                setFullName(userInfo.displayName);
                setEmail(userInfo.email);
            } finally {
                setIsAuthenticating(false);
            }
        }

        getData();
    }, []);

    async function savePressHandler() {
        try {
            setIsAuthenticating(true);
            setLoadingMess("Updating...");
            const response = await updateUserInfo(authCtx.token, email, mobile, fullName, dob);
            if (response.code === 200) {
                Alert.alert(
                    "Success",
                    "Account information has been changed successfully",
                    [{
                        text: "OK"
                    }]
                );
            } else {
                Alert.alert(
                    "Fail",
                    response.message,
                    [{
                        text: "OK"
                    }]
                );
            }
        } catch (e) {
            console.log(e);
            Alert.alert(
                "Fail",
                "An error occurred, please try again later",
                [{
                    text: "OK"
                }]
            );
        } finally {
            setIsAuthenticating(false);
        }
    }

    if (isAuthenticating) {
        return <LoadingOverlay message={loadingMess}/>;
    }

    return (
        <>
            <View style={{flex: 1}}>
                <ScrollView keyboardShouldPersistTaps='handled' contentContainerStyle={styles.container}>
                    <View style={styles.avatarContainer}>
                        <Image style={styles.avatar} source={require('../assets/avatar.png')}/>
                        <Feather style={styles.camera} name="camera" size={24} color={Colors.primary100}/>
                    </View>

                    <View style={styles.inputsContainer}>
                        <Input label='Fullname' value={fullName}
                               onUpdateValue={(enteredValue) => setFullName(enteredValue)}/>

                        <Input label='Email' keyboardType='email-address' isDisable={true} value={email}
                               onUpdateValue={(enteredValue) => setEmail(enteredValue)}/>

                        {/*<Input label='Mobile' keyboardType='phone-pad' value={mobile}*/}
                        {/*       onUpdateValue={(enteredValue) => setMobile(enteredValue)} />*/}

                        {/*<View style={styles.dobContainer}>*/}
                        {/*  <Text style={styles.dobText}>DOB</Text>*/}
                        {/*  <DatePicker defaultValue={dob} onUpdateDate={(dob) => setDob(dob)} />*/}
                        {/*</View>*/}
                    </View>

                    <View style={styles.buttonContainer}>
                        <Button style={styles.button} onPress={savePressHandler}>Save</Button>
                    </View>
                </ScrollView>
            </View>
            {SHOW_BANNER_AD && <BannerAd
                unitId={BANNER_AD_UNIT_ID}
                size={BannerAdSize.ANCHORED_ADAPTIVE_BANNER}
                requestOptions={{
                    requestNonPersonalizedAdsOnly: true,
                }}
            />}
        </>
    );
}

export default AccountSettingScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1
    },
    avatarContainer: {
        flexDirection: 'row',
        marginTop: 30,
        alignItems: 'flex-end',
        justifyContent: 'center',
        display: 'none'
    },
    avatar: {
        width: 100,
        height: 100,
        borderRadius: 50
    },
    camera: {
        position: 'relative',
        left: -32
    },
    inputsContainer: {
        marginTop: 20,
        marginHorizontal: 10
    },
    buttonContainer: {
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: 40
    },
    button: {
        width: 237,
        height: 50
    },
    dobContainer: {
        marginTop: 25
    },
    dobText: {
        color: Colors.accent100,
        marginBottom: 4,
        fontSize: 14,
        fontWeight: '500'
    }
});
