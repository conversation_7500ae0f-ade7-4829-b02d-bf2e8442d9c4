import {Platform, Pressable, StyleSheet, Text, View} from 'react-native';
import {useState} from 'react';
import {Colors} from '../constants/styles';
import {Feather} from '@expo/vector-icons';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {formatDate} from '../util/DateUtils';

function DatePicker({defaultValue, onUpdateDate}) {
  const [isVisible, setVisible] = useState(false);
  const [value, setValue] = useState(defaultValue);

  function showDatePicker() {
    setVisible(true);
  }

  function hideDatePicker() {
    setVisible(false);
  }

  function handleConfirm(date) {
    setValue(formatDate(date));
    hideDatePicker();
    onUpdateDate(date);
  }

  return (
    <Pressable onPress={showDatePicker}>
      <View style={styles.container}>
        <Text style={styles.text}>{value}</Text>

        <Feather name='calendar' size={24} color={Colors.primary100} />

        <DateTimePickerModal
          isVisible={isVisible}
          onConfirm={handleConfirm}
          onCancel={hideDatePicker}
        />
      </View>
    </Pressable>
  );
}

export default DatePicker;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: "space-between",
    alignItems: "center",
    height: 55,
    paddingHorizontal: 6,
    borderRadius: 4,
    elevation: 4,
    backgroundColor: 'white',
    shadowColor: 'black',
    shadowOpacity: 0.25,
    shadowOffset: {width: 0, height: 2},
    shadowRadius: 8,
    overflow: Platform.select({ios: 'visible', android: 'hidden'})
  },
  text: {
    fontSize: 16,
    color: Colors.accent100,
  }
});
