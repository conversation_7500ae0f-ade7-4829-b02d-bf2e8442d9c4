import {createContext, useState} from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";

export const AppContext = createContext({
  isFirstLaunch: false,
  finishOnboarding: () => {
  },
  loadOnboardingState: () => {
  }
});

function AppProvider({children}) {
  const [isFirstLaunch, setIsFirstLaunch] = useState(false);

  async function finishOnboarding() {
    try {
      setIsFirstLaunch(false);
      await AsyncStorage.setItem('show_onboarding', "false");
    } catch (ex) {
      console.log(ex)
    }
  }

  async function loadOnboardingState() {
    try {
      const hasFirstLaunched = await AsyncStorage.getItem("show_onboarding");
      setIsFirstLaunch(hasFirstLaunched == null);
    } catch (ex) {
      console.log(ex)
      setIsFirstLaunch(false);
    }
  }

  const value = {
    isFirstLaunch: isFirstLaunch,
    finishOnboarding: finishOnboarding,
    loadOnboardingState: loadOnboardingState
  }

  return <AppContext.Provider value={value}>{children}</AppContext.Provider>
}

export default AppProvider;
